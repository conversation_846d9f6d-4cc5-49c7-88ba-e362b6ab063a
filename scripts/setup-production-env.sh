#!/bin/bash
# 生产环境环境变量设置脚本
# 此脚本仅在腾讯云生产服务器上执行

set -e

echo "🔧 公职猫生产环境变量设置"
echo "=========================="

# 检查运行环境
if [ "$USER" != "root" ]; then
    echo "❌ 请使用root用户执行此脚本"
    exit 1
fi

# 检查是否为生产环境
echo "⚠️  警告：此脚本将设置生产环境敏感配置"
echo "请确认您在腾讯云生产服务器上执行 (输入 'YES' 确认):"
read confirmation

if [ "$confirmation" != "YES" ]; then
    echo "操作已取消"
    exit 1
fi

# 创建安全目录
echo "📁 创建配置目录..."
mkdir -p /etc/gongzhimall
mkdir -p /var/log/gongzhimall
mkdir -p /var/www/cache
mkdir -p /var/backups/gongzhimall

# 设置目录权限
chmod 700 /etc/gongzhimall
chmod 755 /var/log/gongzhimall
chmod 755 /var/www/cache
chmod 755 /var/backups/gongzhimall

# 创建生产环境变量文件
echo "🔐 创建环境变量文件..."
cat > /etc/gongzhimall/production.env << 'EOF'
# 公职猫微信转发服务 - 生产环境配置
# 此文件包含敏感信息，请妥善保管

NODE_ENV=production

# ==================== 腾讯云配置 ====================
TENCENT_SECRET_ID=AKID0tmEXfhYCdOxLVSCl7FAuR1Ulofs7D8q
TENCENT_SECRET_KEY=Zt2zwgLuNeiYCest78wYxBMK8jBQUMUe

# ==================== 服务器配置 ====================
PORT=3000
SERVER_DOMAIN=wechat.api.gongzhimall.com
FILE_STORAGE_PATH=/var/www/cache
MAX_FILE_SIZE=104857600
FILE_CLEANUP_INTERVAL=3600000
FILE_MAX_AGE_DAYS=3

# ==================== 数据库配置 ====================
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=wechat_user
MYSQL_PASSWORD=wechat@gongzhimall123
MYSQL_DATABASE=gongzhimall_wechat
MYSQL_CONNECTION_LIMIT=10
MYSQL_QUEUE_LIMIT=0

# ==================== 企业微信配置 ====================
WECHAT_CORP_ID=ww857dc7bfe97b085b
WECHAT_CORP_SECRET=eQs0lmS8uUgpyDM74XvHHwWuAddq1n_qhdNsBZoB13I
WECHAT_AGENT_ID=1000002
WECHAT_API_BASE=https://qyapi.weixin.qq.com
WECHAT_TOKEN=gongzhimallEnterprise2025
WECHAT_ENCODING_AES_KEY=zlb6q9HgvrGOYlgMR9HNmMNIHo9dJgrQVmbs7G2DJcv
WECHAT_DEFAULT_OPEN_KFID=kfce062e9c6b4dc4f49
WECHAT_BINDING_SECRET=gongzhimall_binding_secret_2025
WECHAT_API_TIMEOUT=30000
WECHAT_API_RETRY_COUNT=3

# ==================== 极光推送配置 ====================
JPUSH_APP_KEY=bd2c958b83dc679759a25664
JPUSH_MASTER_SECRET=59a9af913b1d2dd85f4cbc24
JPUSH_CHANNEL=gongzhimall-official

# ==================== 应用配置 ====================
TOKEN_SECRET=gongzhimall-app-2025
WECHAT_BINDING_TOKEN_SECRET=gongzhimall_binding_secret_2025
FILE_ENCRYPTION_KEY=gongzhimall-file-encryption-2025
DOWNLOAD_TOKEN_EXPIRES_HOURS=24

# ==================== 微信开放平台配置 ====================
WECHAT_APP_ID=wx_your_app_id
WECHAT_APP_SECRET=6bea22ca7c5993c747ab3a58de0caf06
WECHAT_PUSH_TOKEN=YBvT4DCD
WECHAT_PUSH_ENCODING_AES_KEY=DGDPGuTVSC8I42PxuLbXGLMQO6PeGEmEa8ttmJmuMLh

# ==================== 安全配置 ====================
CORS_ORIGIN=*
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=900000

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
ENABLE_DB_LOGGING=true
DEBUG=false
LOG_FILE_PATH=/var/log/gongzhimall
LOG_FILE_MAX_SIZE=10485760
LOG_FILE_MAX_FILES=5

# ==================== 监控配置 ====================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health
ENABLE_PERFORMANCE_MONITORING=true

# ==================== Docker配置 ====================
DOCKER_CONTAINER_NAME=gongzhimall-wechat
DOCKER_RESTART_POLICY=unless-stopped
EOF

# 设置文件权限（仅root可读写）
chmod 600 /etc/gongzhimall/production.env
chown root:root /etc/gongzhimall/production.env

echo "✅ 环境变量文件创建完成: /etc/gongzhimall/production.env"

# 创建移动端环境变量文件
echo "📱 创建移动端环境变量文件..."
cat > /etc/gongzhimall/mobile.env << 'EOF'
# 公职猫移动端 - 生产环境配置

NODE_ENV=production

# 微信企业应用配置
WECHAT_CORP_ID=ww857dc7bfe97b085b
WECHAT_CORP_SECRET=eQs0lmS8uUgpyDM74XvHHwWuAddq1n_qhdNsBZoB13I
WECHAT_AGENT_ID=1000002

# 微信开放平台配置
WECHAT_OPEN_APP_ID=wx_your_app_id
WECHAT_OPEN_APP_SECRET=6bea22ca7c5993c747ab3a58de0caf06

# 微信服务号配置
WECHAT_SERVICE_APP_ID=wx_your_service_app_id
WECHAT_SERVICE_APP_SECRET=your_service_app_secret
WECHAT_SERVICE_TOKEN=your_service_token
WECHAT_SERVICE_ENCODING_AES_KEY=your_service_encoding_aes_key

# 极光推送配置
JPUSH_APP_KEY=bd2c958b83dc679759a25664
JPUSH_CHANNEL=gongzhimall-official

# API配置
WECHAT_BINDING_API_BASE_URL=https://wechat.api.gongzhimall.com
WECHAT_BINDING_TOKEN_SECRET=gongzhimall_binding_secret_2025
WECHAT_BINDING_MOBILE_APP_TOKEN=mobile-app-token

# 推送配置
WECHAT_PUSH_TOKEN=YBvT4DCD
WECHAT_PUSH_ENCODING_AES_KEY=DGDPGuTVSC8I42PxuLbXGLMQO6PeGEmEa8ttmJmuMLh
EOF

chmod 600 /etc/gongzhimall/mobile.env
chown root:root /etc/gongzhimall/mobile.env

echo "✅ 移动端环境变量文件创建完成: /etc/gongzhimall/mobile.env"

# 创建环境变量加载脚本
echo "🔄 创建环境变量加载脚本..."
cat > /etc/gongzhimall/load-env.sh << 'EOF'
#!/bin/bash
# 环境变量加载脚本

# 加载生产环境变量
if [ -f "/etc/gongzhimall/production.env" ]; then
    export $(cat /etc/gongzhimall/production.env | grep -v '^#' | xargs)
fi

# 加载移动端环境变量
if [ -f "/etc/gongzhimall/mobile.env" ]; then
    export $(cat /etc/gongzhimall/mobile.env | grep -v '^#' | xargs)
fi
EOF

chmod 755 /etc/gongzhimall/load-env.sh
chown root:root /etc/gongzhimall/load-env.sh

echo "✅ 环境变量加载脚本创建完成: /etc/gongzhimall/load-env.sh"

# 添加到系统环境变量
echo "🔗 添加到系统环境变量..."
if ! grep -q "source /etc/gongzhimall/load-env.sh" /etc/profile; then
    echo "source /etc/gongzhimall/load-env.sh" >> /etc/profile
fi

echo "📋 配置摘要："
echo "  - 环境变量文件: /etc/gongzhimall/production.env"
echo "  - 移动端配置: /etc/gongzhimall/mobile.env"
echo "  - 加载脚本: /etc/gongzhimall/load-env.sh"
echo "  - 日志目录: /var/log/gongzhimall"
echo "  - 缓存目录: /var/www/cache"
echo "  - 备份目录: /var/backups/gongzhimall"

echo ""
echo "🎉 生产环境变量设置完成！"
echo ""
echo "⚠️  重要提醒："
echo "1. 环境变量文件包含敏感信息，请妥善保管"
echo "2. 重新登录或执行 'source /etc/profile' 使环境变量生效"
echo "3. 可以使用 'source /etc/gongzhimall/load-env.sh' 手动加载环境变量"
echo "4. 定期备份 /etc/gongzhimall/ 目录"
