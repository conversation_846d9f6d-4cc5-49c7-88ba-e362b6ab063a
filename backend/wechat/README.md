# 公职猫微信转发服务

## 📋 概述

公职猫微信转发服务是一个基于腾讯云轻量应用服务器的企业级消息转发服务，专门用于接收企业微信消息、下载并加密缓存文件，然后安全地转发到公职猫客户端。采用**临时缓存架构**，严格遵循"U盘级私密"的设计理念。

## 🔧 核心功能

### 1. 企业微信消息接收与处理
- **URL验证**: 支持企业微信Webhook配置时的URL验证
- **消息处理**: 接收并处理企业微信推送的各类消息（文本、图片、语音、视频、文件等）
- **签名验证**: 严格的消息签名验证机制，确保消息安全
- **XML解析**: 自动解析企业微信XML格式消息

### 2. 临时缓存架构 (Temporary Cache Architecture)
- **代理文件下载**: 从企业微信API下载媒体文件
- **加密存储**: 将文件加密后临时存储在服务器本地
- **阅后即焚**: 文件下载后或3天后自动删除
- **大文件支持**: 无执行时长限制，支持大文件处理
- **流式传输**: 支持断点续传和流式下载

### 3. 用户绑定管理
- **绑定查询**: 根据用户UUID查询绑定状态
- **自动绑定**: 通过加密令牌实现用户与企业微信的自动绑定
- **多设备支持**: 支持用户多设备间的消息同步

### 4. 消息同步服务
- **增量同步**: 高效的增量消息同步机制
- **状态管理**: 设备同步状态的实时更新
- **推送通知**: 极光推送(JPush)集成，实时通知用户

### 5. 安全文件下载
- **一次性令牌**: 为客户端提供一次性下载令牌
- **自动清理**: 下载完成后立即删除缓存文件
- **访问控制**: 严格的下载权限验证

## 🚀 API接口

### 系统监控
- `GET /health` - 健康检查

### 企业微信Webhook
- `GET /api/wechat/webhook` - URL验证
- `POST /api/wechat/webhook` - 消息处理

### 用户绑定
- `GET /api/bind/status` - 查询绑定状态
- `POST /api/bind/create` - 创建绑定令牌

### 消息同步
- `GET /api/sync/messages` - 增量同步消息（包含下载链接）
- `POST /api/sync/ack` - 确认消息同步
- `POST /api/sync/register-device` - 注册设备

### 文件下载
- `GET /api/media/download/:token` - 安全文件下载

### 推送服务
- `POST /api/push/send` - 发送推送通知

详细的API文档请参考 [docs/API_SPECIFICATION_V2.md](./docs/API_SPECIFICATION_V2.md)

## 🛠 技术栈

### 核心依赖
- **Node.js**: 18.15.0+
- **Express**: 4.18.2 - Web框架
- **MySQL2**: 3.14.2 - 数据库驱动
- **Axios**: 1.6.0 - HTTP客户端
- **Crypto-js**: 4.2.0 - 加密库
- **JPush SDK**: 3.5.0 - 推送服务

### 安全与性能
- **Helmet**: 7.0.0 - 安全中间件
- **Express-rate-limit**: 6.11.2 - 请求限制
- **自定义日志系统**: 支持数据库存储和详细元数据记录
- **CORS**: 2.8.5 - 跨域支持

### 文件处理
- **Multer**: 1.4.5-lts.1 - 文件上传
- **Form-data**: 4.0.0 - 表单数据处理

### 工具库
- **UUID**: 9.0.0 - 唯一标识符
- **Node-cron**: 3.0.2 - 定时任务
- **Jsonwebtoken**: 9.0.2 - JWT令牌

## 🔐 部署配置

### 环境变量配置
```bash
# 腾讯云配置
TENCENT_SECRET_ID=your_secret_id
TENCENT_SECRET_KEY=your_secret_key

# 服务器配置
PORT=3000
SERVER_DOMAIN=wechat.api.gongzhimall.com
FILE_STORAGE_PATH=/var/www/cache
MAX_FILE_SIZE=104857600  # 100MB

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=wechat_user
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=gongzhimall_wechat

# 企业微信配置
WECHAT_CORP_ID=your_corp_id
WECHAT_CORP_SECRET=your_corp_secret
WECHAT_AGENT_ID=your_agent_id
WECHAT_TOKEN=your_token
WECHAT_ENCODING_AES_KEY=your_aes_key

# 推送服务配置
JPUSH_APP_KEY=your_jpush_app_key
JPUSH_MASTER_SECRET=your_jpush_master_secret

# 安全配置
TOKEN_SECRET=your_token_secret
FILE_ENCRYPTION_KEY=your_file_encryption_key
WECHAT_BINDING_TOKEN_SECRET=your_binding_secret

# 应用配置
NODE_ENV=production
CORS_ORIGIN=*
RATE_LIMIT_MAX=100
```

完整的环境变量模板请参考 [config/env.template](./config/env.template)

### 部署步骤

#### 1. 环境准备
```bash
# 安装Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装MySQL（宝塔面板或云服务器MySQL分支）
# 宝塔面板：在面板中安装MySQL
# 云服务器：使用系统包管理器安装MySQL分支
sudo apt-get install mysql-server

# 创建数据库和用户
mysql -u root -p
CREATE DATABASE gongzhimall_wechat;
CREATE USER 'wechat_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON gongzhimall_wechat.* TO 'wechat_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 2. 应用部署
```bash
# 克隆项目
git clone https://gitee.com/gongzhimall/gongzhimall.git
cd gongzhimall/backend/wechat

# 安装依赖
npm install --production

# 配置环境变量
cp config/env.template .env
# 编辑 .env 文件，填写实际配置

# 构建并初始化数据库
cd data/schema && node scripts/build-schema.js
mysql -u wechat_user -p gongzhimall_wechat < data/schema/build/schema.sql

# 启动服务
npm start
```

#### 3. PM2进程管理
```bash
# 安装PM2
npm install -g pm2

# 使用PM2启动服务
pm2 start ecosystem.config.js --env production

# 查看状态
pm2 status
pm2 logs gongzhimall-wechat

# 重启服务
pm2 reload gongzhimall-wechat
```

#### 4. Docker部署（可选）
```bash
# 构建镜像
npm run docker:build

# 启动容器
npm run docker:run

# 停止容器
npm run docker:stop
```

## 🔐 安全特性

### 数据安全
- **文件加密存储**: 所有文件使用AES加密存储
- **一次性下载令牌**: 防止文件泄露和重复下载
- **阅后即焚机制**: 文件下载后自动删除
- **签名验证**: HMAC-SHA256签名验证所有API请求

### 访问控制
- **令牌认证**: JWT令牌验证用户身份
- **请求限制**: 防止API滥用和DDoS攻击
- **CORS保护**: 严格的跨域访问控制
- **环境隔离**: 生产环境强制使用环境变量

### 日志安全
- **敏感信息脱敏**: 自动脱敏处理日志中的敏感数据
- **访问日志**: 记录所有API访问和文件下载
- **错误追踪**: 详细的错误堆栈和上下文信息

## 📊 监控与日志

### 系统监控
- **健康检查**: `/health` 端点提供系统状态
- **内存监控**: 自动监控内存使用情况
- **文件缓存监控**: 实时统计缓存文件数量和大小
- **数据库连接监控**: 监控数据库连接状态

### 日志管理
- **访问日志**: 自定义日志系统记录HTTP请求（支持数据库存储）
- **错误日志**: 详细的错误堆栈和上下文
- **业务日志**: 记录关键业务操作
- **日志轮转**: 自动日志文件轮转和清理

### 定时任务
- **文件清理**: 定期清理过期文件
- **数据库维护**: 定期清理无效数据
- **健康检查**: 定期检查系统组件状态

## 🔄 消息处理流程

### 1. 消息接收流程
```
企业微信 → Webhook验证 → 签名验证 → XML解析 → 消息分类 → 存储元数据
```

### 2. 文件处理流程
```
文件消息 → 下载文件 → 加密存储 → 生成下载令牌 → 推送通知 → 客户端下载 → 自动删除
```

### 3. 同步流程
```
客户端请求 → 验证身份 → 查询增量消息 → 返回元数据 → 客户端确认 → 更新同步状态
```

## 📝 开发说明

### 项目结构
```
backend/wechat/
├── index.js                  # 服务器入口文件
├── ecosystem.config.js       # PM2配置文件
├── package.json             # 项目配置和依赖
├── api/                     # API层
│   ├── controller.js        # 控制器层
│   └── errorHandler.js      # 错误处理
├── service/                 # 业务逻辑层
│   ├── wechatApi.js         # 企业微信API
│   ├── MessageProcessService.js # 消息处理服务
│   ├── UserBindingService.js    # 用户绑定服务
│   ├── pushService.js       # 推送服务
│   ├── deviceManager.js     # 设备管理
│   ├── fileService.js       # 文件服务
│   ├── MediaDownloadService.js # 媒体下载服务
│   ├── MessageSyncService.js   # 消息同步服务
│   └── WebhookService.js    # Webhook服务
├── data/                    # 数据层
│   ├── database/            # 数据库操作模块
│   │   ├── index.js         # 数据库主入口
│   │   ├── core.js          # 核心数据库操作
│   │   ├── business.js      # 业务数据库操作
│   │   ├── cache.js         # 缓存管理
│   │   ├── partitions.js    # 分区管理
│   │   └── cleanup.js       # 清理机制
│   └── schema/              # 统一数据库架构管理
│       ├── README.md        # 架构管理说明
│       ├── core/            # 核心表结构
│       │   ├── 01_users.sql # 用户相关表
│       │   ├── 02_bindings.sql # 绑定相关表
│       │   ├── 03_messages.sql # 消息相关表
│       │   └── 04_system.sql # 系统相关表
│       ├── features/        # 功能特性表
│       │   ├── 01_cache.sql # 缓存相关表
│       │   └── 02_views.sql # 视图定义
│       ├── environments/    # 环境特定配置
│       │   ├── development.sql # 开发环境配置
│       │   └── production.sql # 生产环境配置
│       ├── scripts/         # 构建脚本
│       │   └── build-schema.js # 架构构建脚本
│       └── build/           # 构建输出
│           └── schema.sql   # 最终生成的架构文件
├── config/                  # 配置
│   └── env.template         # 环境变量模板
├── deploy/                  # 部署相关
│   └── ...                  # 部署脚本和配置
├── scripts/                 # 工具脚本
│   ├── cleanup-files.js     # 文件清理脚本
│   └── cleanup-database.js  # 数据库清理脚本
├── test-files/              # 测试文件
│   ├── large_text.txt       # 大文件测试
│   ├── test.csv             # CSV文件测试
│   ├── test.html            # HTML文件测试
│   ├── test.json            # JSON文件测试
│   ├── test.txt             # 文本文件测试
│   ├── test-*.js            # 功能测试脚本
│   └── verify-*.js          # 配置验证脚本
├── docs/                    # 文档
│   └── API_SPECIFICATION_V2.md # API文档
└── test-files/              # 测试文件
```

### 开发环境
- **Node.js**: 18.15.0+
- **MySQL**: 宝塔面板MySQL或云服务器MySQL分支
- **腾讯云轻量应用服务器**: Ubuntu 20.04+

### 开发命令
```bash
# 开发模式启动
npm run dev

# 生产模式启动
npm start

# 代码验证
npm run validate

# 测试命令
npm run test-end-to-end      # 端到端测试
npm run test-push            # 推送通知测试
npm run test-file-detection  # 文件类型检测测试
npm run test-services        # 服务重构测试
npm run test-architecture    # 新架构测试

# 验证命令
npm run verify-push          # 推送配置验证
npm run verify-binding       # 绑定加密验证

# 清理命令
npm run cleanup-files        # 文件清理
npm run cleanup-database     # 数据库清理
```

## 📞 技术支持

### 文档资源
- **API文档**: [docs/API_SPECIFICATION_V2.md](./docs/API_SPECIFICATION_V2.md)
- **部署指南**: 查看部署相关脚本和配置

### 联系方式
- **项目仓库**: https://gitee.com/gongzhimall/gongzhimall
- **官方网站**: https://www.gongzhimall.com
- **问题反馈**: https://gitee.com/gongzhimall/gongzhimall/issues

### 常见问题
1. **文件下载失败**: 检查下载令牌是否有效，文件是否已过期
2. **推送通知不工作**: 验证JPush配置是否正确
3. **数据库连接失败**: 检查MySQL服务状态和连接配置
4. **企业微信消息接收失败**: 验证Webhook配置和签名

---

**版本**: 1.0.0  
**最后更新**: 2025年1月  
**维护者**: 芝麻巧匠 