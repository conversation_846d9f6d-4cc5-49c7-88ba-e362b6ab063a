/**
 * 用户绑定服务
 * 专门处理用户绑定相关逻辑
 */

const crypto = require('crypto');
const CryptoJS = require('crypto-js');
const db = require('../data/database');
const wechatApi = require('./wechatApi');
const { logInfo, logError } = require('../api/errorHandler');

/**
 * 根据用户UUID查询绑定信息
 */
const getBindingByUserUuid = async (userUuid) => {
  try {
    const binding = await db.getBindingByUserUuid(userUuid);
    if (binding) {
      return {
        user_uuid: binding.user_uuid,
        external_userid: binding.external_userid,
        binding_status: binding.binding_status,
        binding_time: binding.binding_time
      };
    }
    return null;
  } catch (error) {
    logError('根据用户UUID查询绑定信息失败:', error);
    return null;
  }
};

/**
 * 根据external_userid查询绑定信息
 */
const getBindingByExternalUserId = async (externalUserId) => {
  try {
    const binding = await db.getBindingByExternalUserId(externalUserId);
    if (binding) {
      return {
        user_uuid: binding.user_uuid,
        external_userid: binding.external_userid,
        binding_status: binding.binding_status,
        binding_time: binding.binding_time
      };
    }
    return null;
  } catch (error) {
    logError('根据external_userid查询绑定信息失败:', error);
    return null;
  }
};

/**
 * 解除用户绑定
 */
const unbindUser = async (userUuid) => {
  try {
    logInfo('🔓 开始解除用户绑定:', userUuid);

    // 1. 检查用户是否已绑定
    const existingBinding = await getBindingByUserUuid(userUuid);
    if (!existingBinding || existingBinding.binding_status !== 'active') {
      return {
        success: false,
        message: '用户未绑定或绑定状态异常'
      };
    }

    // 2. 删除绑定关系
    const result = await db.deleteBinding(userUuid);
    if (result.success) {
      logInfo('✅ 用户绑定解除成功:', userUuid);
      return {
        success: true,
        message: '用户绑定解除成功'
      };
    } else {
      return {
        success: false,
        message: '绑定删除失败'
      };
    }
  } catch (error) {
    logError('解除用户绑定异常:', error);
    return {
      success: false,
      message: '解除用户绑定失败: ' + error.message
    };
  }
};

/**
 * 解密绑定令牌
 */
const decryptBindingToken = async (encryptedToken) => {
  try {
    const WECHAT_BINDING_TOKEN_SECRET = process.env.WECHAT_BINDING_TOKEN_SECRET;
    if (!WECHAT_BINDING_TOKEN_SECRET) {
      throw new Error('WECHAT_BINDING_TOKEN_SECRET environment variable is required');
    }

    // 使用AES解密
    const bytes = CryptoJS.AES.decrypt(encryptedToken, WECHAT_BINDING_TOKEN_SECRET);
    const decryptedData = bytes.toString(CryptoJS.enc.Utf8);

    if (!decryptedData) {
      throw new Error('解密结果为空');
    }

    // 解析JSON数据
    const tokenData = JSON.parse(decryptedData);
    
    return {
      success: true,
      data: tokenData
    };
  } catch (error) {
    logError('解密绑定令牌失败:', error);
    return {
      success: false,
      message: '绑定令牌解密失败: ' + error.message
    };
  }
};

/**
 * 向用户所有设备推送绑定成功通知
 */
const pushBindingSuccessToDevices = async (userUuid) => {
  try {
    // 获取用户所有活跃设备
    const devices = await db.query(`
      SELECT device_id, platform, push_token, push_provider
      FROM user_device_bindings
      WHERE user_uuid = ? AND status = 'active'
    `, [userUuid]);

    if (devices.length === 0) {
      logInfo('用户没有活跃设备，跳过推送通知');
      return { success: true };
    }

    // 推送绑定成功通知
    const pushService = require('./pushService');
    for (const device of devices) {
      if (device.push_token) {
        await pushService.sendPushNotification(device.push_token, {
          title: '微信绑定成功',
          body: '您的微信账号已成功绑定到公职猫',
          data: {
            type: 'binding_success',
            user_uuid: userUuid
          }
        }, device.platform);
      }
    }

    return { success: true };
  } catch (error) {
    logError('推送绑定成功通知失败:', error);
    return { success: false, message: error.message };
  }
};

/**
 * 生成微信客服绑定链接
 */
const generateWeChatBindingLink = async (userUuid, openKfId = null, scene = 'binding') => {
  try {
    logInfo('🔗 开始生成微信客服绑定链接:', { userUuid, openKfId, scene });

    // 如果没有指定客服账号ID，使用环境变量中的默认值
    const kfId = openKfId || process.env.WECHAT_DEFAULT_OPEN_KFID;
    if (!kfId) {
      throw new Error('缺少微信客服账号ID配置');
    }

    // 生成绑定链接
    const linkResult = await wechatApi.generateBindingKfLink(kfId, userUuid, scene);
    
    if (linkResult.success) {
      return {
        success: true,
        binding_url: linkResult.binding_url
      };
    } else {
      return {
        success: false,
        message: linkResult.message
      };
    }
  } catch (error) {
    logError('生成微信客服绑定链接失败:', error);
    return {
      success: false,
      message: '生成绑定链接失败: ' + error.message
    };
  }
};

module.exports = {
  getBindingByUserUuid,
  getBindingByExternalUserId,
  unbindUser,
  decryptBindingToken,
  pushBindingSuccessToDevices,
  generateWeChatBindingLink
};
