/**
 * 业务数据库操作模块
 * 包含绑定、消息、设备、推送等业务逻辑
 */

const { query, getOne, insert, update, transaction } = require('./core');
const deviceManager = require('../../service/deviceManager');

// ==================== 业务数据库操作函数 ====================

/**
 * 检查用户是否已绑定微信（优化查询）
 * @param {string} userUuid 用户UUID
 * @returns {Promise<boolean>} 是否已绑定
 */
const isUserWechatBound = async (userUuid) => {
  try {
    // 参数非空验证
    if (!userUuid || typeof userUuid !== 'string') {
      throw new Error('userUuid参数无效：必须是非空字符串');
    }

    if (userUuid.trim().length === 0) {
      throw new Error('userUuid不能为空字符串');
    }

    const sql = `
      SELECT 1 FROM wechat_bindings 
      WHERE user_uuid = ? AND binding_status = 'active' 
      LIMIT 1
    `;
    const result = await getOne(sql, [userUuid]);
    return result !== null;
  } catch (error) {
    console.error('检查用户微信绑定状态失败:', error);
    throw error;
  }
};

/**
 * 获取用户绑定状态详情（包含绑定信息）
 * @param {string} userUuid 用户UUID
 * @returns {Promise<Object|null>} 绑定状态详情或null
 */
const getUserBindingStatus = async (userUuid) => {
  try {
    // 参数非空验证
    if (!userUuid || typeof userUuid !== 'string') {
      throw new Error('userUuid参数无效：必须是非空字符串');
    }

    if (userUuid.trim().length === 0) {
      throw new Error('userUuid不能为空字符串');
    }

    const sql = `
      SELECT 
        au.user_uuid,
        au.device_count,
        CASE 
          WHEN wb.user_uuid IS NOT NULL AND wb.binding_status = 'active' 
          THEN true 
          ELSE false 
        END as is_bound,
        wb.external_userid,
        wb.binding_status,
        wb.created_at as binding_time
      FROM app_users au
      LEFT JOIN wechat_bindings wb ON au.user_uuid = wb.user_uuid AND wb.binding_status = 'active'
      WHERE au.user_uuid = ?
    `;
    return await getOne(sql, [userUuid]);
  } catch (error) {
    console.error('获取用户绑定状态详情失败:', error);
    throw error;
  }
};

/**
 * 根据external_userid查询绑定信息
 * @param {string} externalUserId 企业微信外部用户ID
 * @returns {Promise<Object|null>} 绑定信息或null
 */
const getBindingByExternalUserId = async (externalUserId) => {
  try {
    // 参数非空验证
    if (!externalUserId || typeof externalUserId !== 'string') {
      throw new Error('externalUserId参数无效：必须是非空字符串');
    }

    if (externalUserId.trim().length === 0) {
      throw new Error('externalUserId不能为空字符串');
    }

    const sql = `
      SELECT user_uuid, external_userid, binding_status, created_at as binding_time
      FROM wechat_bindings
      WHERE external_userid = ? AND binding_status = 'active'
    `;
    return await getOne(sql, [externalUserId]);
  } catch (error) {
    console.error('查询外部用户绑定信息失败:', error);
    throw error;
  }
};

/**
 * 创建或更新绑定关系
 * @param {string} userUuid 用户UUID
 * @param {string} externalUserId 企业微信外部用户ID
 * @returns {Promise<Object>} 操作结果
 */
const createOrUpdateBinding = async (userUuid, externalUserId) => {
  try {
    // 参数非空验证
    if (!userUuid || typeof userUuid !== 'string') {
      throw new Error('userUuid参数无效：必须是非空字符串');
    }

    if (!externalUserId || typeof externalUserId !== 'string') {
      throw new Error('externalUserId参数无效：必须是非空字符串');
    }

    // 参数格式验证
    if (userUuid.trim().length === 0) {
      throw new Error('userUuid不能为空字符串');
    }

    if (externalUserId.trim().length === 0) {
      throw new Error('externalUserId不能为空字符串');
    }

    return await transaction(async (connection) => {
      // 检查是否已存在绑定
      const [existingBinding] = await connection.execute(
        'SELECT id, user_uuid, external_userid FROM wechat_bindings WHERE user_uuid = ? OR external_userid = ?',
        [userUuid, externalUserId]
      );
      
      // 日志记录
      console.log('【绑定检查】userUuid:', userUuid, 'externalUserId:', externalUserId);
      if (existingBinding.length > 0) {
        console.log('【绑定检查】找到已存在的绑定:', existingBinding);
      } else {
        console.log('【绑定检查】未找到绑定关系，将创建新绑定。');
      }

      if (existingBinding.length > 0) {
        // 更新现有绑定
        const [updateResult] = await connection.execute(`
          UPDATE wechat_bindings 
          SET user_uuid = ?, external_userid = ?, binding_status = 'active', 
              binding_token = NULL, token_expires_at = NULL, updated_at = NOW()
          WHERE id = ?
        `, [userUuid, externalUserId, existingBinding[0].id]);
        
        console.log('【绑定更新】更新结果:', updateResult);

        if (updateResult.affectedRows > 0) {
          return {
            success: true,
            binding: {
              user_uuid: userUuid,
              external_userid: externalUserId,
              binding_status: 'active',
              binding_time: new Date()
            }
          };
        }
      } else {
        // 创建新绑定
        const [insertResult] = await connection.execute(`
          INSERT INTO wechat_bindings (user_uuid, external_userid, binding_status)
          VALUES (?, ?, 'active')
        `, [userUuid, externalUserId]);
        
        console.log('【绑定创建】插入结果:', insertResult);

        if (insertResult.affectedRows > 0) {
          return {
            success: true,
            binding: {
              user_uuid: userUuid,
              external_userid: externalUserId,
              binding_status: 'active',
              binding_time: new Date()
            }
          };
        }
      }
      
      return {
        success: false,
        message: '绑定操作失败'
      };
    });
  } catch (error) {
    console.error('创建或更新绑定关系失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 删除用户绑定关系
 * @param {string} userUuid 用户UUID
 * @returns {Promise<Object>} 操作结果
 */
const deleteBinding = async (userUuid) => {
  try {
    // 参数非空验证
    if (!userUuid || typeof userUuid !== 'string') {
      throw new Error('userUuid参数无效：必须是非空字符串');
    }

    console.log('【删除绑定】开始删除用户绑定:', userUuid);

    return await transaction(async (connection) => {
      // 删除绑定记录
      const [deleteResult] = await connection.execute(`
        DELETE FROM wechat_bindings
        WHERE user_uuid = ? AND binding_status = 'active'
      `, [userUuid]);

      console.log('【删除绑定】删除结果:', deleteResult);

      if (deleteResult.affectedRows > 0) {
        return {
          success: true,
          message: '绑定删除成功'
        };
      } else {
        return {
          success: false,
          message: '未找到活跃的绑定记录'
        };
      }
    });
  } catch (error) {
    console.error('删除绑定关系失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 保存消息元数据（支持新的文件缓存机制）
 * @param {Object} messageData 消息数据
 * @returns {Promise<Object>} 操作结果
 */
const saveMessageMetadata = async (messageData) => {
  try {
    const {
      user_uuid,
      external_userid,
      wechat_message_id,
      message_type,
      content,
      file_name,
      file_path,
      file_size,
      content_type,
      // 新增的文件类型字段
      file_extension,
      mime_type,
      original_mime_type,
      file_magic_detected,
      media_id,
      media_id_expires_at,
      // 位置消息字段
      location_x,
      location_y,
      location_scale,
      location_label,
      // 链接消息字段
      link_title,
      link_description,
      link_url,
      link_pic_url,
      // 事件消息字段
      event_type,
      event_key,
      event_data,
      download_token,
      download_expires_at,
      file_expires_at,
      downloaded = false,
      metadata
    } = messageData;
    
    const insertData = {
      user_uuid,
      external_userid,
      wechat_message_id,
      message_type,
      metadata: JSON.stringify(metadata || {}),
      sync_status: 'pending'
    };
    
    // 文本消息内容
    if (content) {
      insertData.content = content;
    }
    
    // 文件缓存机制
    if (file_name) {
      insertData.file_name = file_name;
    }
    if (file_path) {
      insertData.file_path = file_path;
    }
    if (file_size) {
      insertData.file_size = file_size;
    }
    if (content_type) {
      insertData.content_type = content_type;
    }

    // 新增的文件类型字段
    if (file_extension) {
      insertData.file_extension = file_extension;
    }
    if (mime_type) {
      insertData.mime_type = mime_type;
    }
    if (original_mime_type) {
      insertData.original_mime_type = original_mime_type;
    }
    if (file_magic_detected !== undefined) {
      insertData.file_magic_detected = file_magic_detected;
    }

    if (media_id) {
      insertData.media_id = media_id;
    }
    if (media_id_expires_at) {
      insertData.media_id_expires_at = media_id_expires_at;
    }

    // 位置消息字段
    if (location_x !== undefined) {
      insertData.location_x = location_x;
    }
    if (location_y !== undefined) {
      insertData.location_y = location_y;
    }
    if (location_scale !== undefined) {
      insertData.location_scale = location_scale;
    }
    if (location_label) {
      insertData.location_label = location_label;
    }

    // 链接消息字段
    if (link_title) {
      insertData.link_title = link_title;
    }
    if (link_description) {
      insertData.link_description = link_description;
    }
    if (link_url) {
      insertData.link_url = link_url;
    }
    if (link_pic_url) {
      insertData.link_pic_url = link_pic_url;
    }

    // 事件消息字段
    if (event_type) {
      insertData.event_type = event_type;
    }
    if (event_key) {
      insertData.event_key = event_key;
    }
    if (event_data) {
      insertData.event_data = JSON.stringify(event_data);
    }
    if (download_token) {
      insertData.download_token = download_token;
    }
    if (download_expires_at) {
      insertData.download_expires_at = download_expires_at;
    }
    if (file_expires_at) {
      insertData.file_expires_at = file_expires_at;
    }
    if (downloaded !== undefined) {
      insertData.downloaded = downloaded;
    }
    
    const result = await insert('wechat_message_logs', insertData);
    
    if (result.success) {
      return {
        success: true,
        message_id: result.insertId,
        download_expires_at,
        file_expires_at
      };
    } else {
      return {
        success: false,
        message: result.error
      };
    }
  } catch (error) {
    console.error('保存消息元数据失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 标记文件已下载（阅后即焚）
 * @param {string} messageId 消息ID
 * @returns {Promise<Object>} 操作结果
 */
const markFileAsDownloaded = async (messageId) => {
  try {
    const result = await query(`
      UPDATE wechat_message_logs 
      SET downloaded = TRUE, 
          downloaded_at = NOW(),
          updated_at = NOW()
      WHERE wechat_message_id = ?
    `, [messageId]);
    
    return {
      success: true,
      affected_rows: result.affectedRows
    };
  } catch (error) {
    console.error('标记文件已下载失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 获取增量消息
 * @param {string} userUuid 用户UUID
 * @param {string} deviceId 设备ID
 * @param {number} sinceId 起始消息ID
 * @param {number} limit 限制数量
 * @returns {Promise<Object>} 查询结果
 */
const getIncrementalMessages = async (userUuid, deviceId, sinceId, limit) => {
  try {
    // 确保参数类型正确，并进行安全验证
    const parsedSinceId = parseInt(sinceId, 10) || 0;
    let parsedLimit = parseInt(limit, 10) || 50;
    
    // 对limit进行安全限制，防止恶意查询
    if (parsedLimit <= 0) {
      parsedLimit = 50;
    }
    if (parsedLimit > 200) { // 设置一个合理的上限
      parsedLimit = 200;
    }
    
    // 为了检查是否还有更多消息，我们请求 limit + 1 条记录
    const queryLimit = parsedLimit + 1;
    
    console.log('执行消息查询:', { userUuid, deviceId, parsedSinceId, queryLimit });
    
    // 查询增量消息（包含文件类型信息）
    // 注意：LIMIT子句不能使用参数绑定，必须直接拼接，因此需要确保queryLimit是安全的整数
    const sql = `
      SELECT id, wechat_message_id, message_type, content,
             file_name, file_path, file_size, content_type,
             file_extension, mime_type, original_mime_type, file_magic_detected,
             media_id, media_id_expires_at,
             download_token, download_expires_at, file_expires_at,
             downloaded, metadata, created_at
      FROM wechat_message_logs
      WHERE user_uuid = ? AND id > ?
      ORDER BY id ASC
      LIMIT ${queryLimit}
    `;
    
    let messages = await query(sql, [userUuid, parsedSinceId]);
    
    // 检查是否还有更多消息
    const hasMore = messages.length > parsedLimit;
    if (hasMore) {
      // 移除多获取的那一条记录
      messages = messages.slice(0, parsedLimit);
    }
    
    const nextSinceId = messages.length > 0 ? messages[messages.length - 1].id : parsedSinceId;
    
    // 移除：设备同步状态的更新将由客户端的ack请求触发
    // if (messages.length > 0) {
    //   const lastMessageId = messages[messages.length - 1].id;
    //   await updateDeviceSyncStatus(userUuid, deviceId, lastMessageId);
    // }
    
    // 处理消息映射，支持新的文件下载机制
    const processedMessages = await Promise.all(messages.map(async (msg) => {
      const baseMessage = {
        id: msg.id,
        wechat_message_id: msg.wechat_message_id,
        message_type: msg.message_type,
        metadata: typeof msg.metadata === 'string' ? JSON.parse(msg.metadata || '{}') : msg.metadata,
        created_at: msg.created_at
      };

      // 处理文本消息
      if (msg.message_type === 'text' && msg.content) {
        baseMessage.content = msg.content;
        return baseMessage;
      }

      // 处理文件类消息（图片、语音、视频、文件）
      if (['image', 'voice', 'video', 'file'].includes(msg.message_type)) {
        // 添加媒体ID信息（用于移动端识别需要下载的媒体）
        if (msg.media_id) {
          baseMessage.media_id = msg.media_id;
          baseMessage.media_id_expires_at = msg.media_id_expires_at;
        }

        // 优先使用新的文件下载机制
        if (msg.download_token && msg.file_name) {
          // 检查下载链接是否过期
          const now = new Date();
          const downloadExpiresAt = new Date(msg.download_expires_at);
          
          if (downloadExpiresAt > now && !msg.downloaded) {
            // 下载链接仍然有效
            baseMessage.download_url = `/api/media/download/${msg.download_token}`;
            baseMessage.download_expires_at = msg.download_expires_at;
            baseMessage.file_name = msg.file_name;
            baseMessage.file_size = msg.file_size;
            baseMessage.content_type = msg.content_type;
          } else if (msg.file_path && !msg.downloaded) {
            // 下载链接过期，但文件仍存在，需要重新生成链接
            try {
              const fileService = require('../../service/fileService');
              const newFileInfo = await fileService.regenerateDownloadLink({
                id: msg.wechat_message_id,
                file_name: msg.file_name,
                file_path: msg.file_path,
                content_type: msg.content_type,
                file_size: msg.file_size
              });
              
              if (newFileInfo) {
                // 更新数据库中的下载令牌
                await query(`
                  UPDATE wechat_message_logs 
                  SET download_token = ?, download_expires_at = ? 
                  WHERE id = ?
                `, [newFileInfo.downloadToken, newFileInfo.downloadExpiresAt, msg.id]);
                
                baseMessage.download_url = newFileInfo.downloadUrl;
                baseMessage.download_expires_at = newFileInfo.downloadExpiresAt;
                baseMessage.file_name = msg.file_name;
                baseMessage.file_size = msg.file_size;
                baseMessage.content_type = msg.content_type;
              } else {
                // 文件不存在，标记为已过期
                baseMessage.file_expired = true;
                baseMessage.error_message = '文件已过期或不存在';
              }
            } catch (error) {
              console.error('重新生成下载链接失败:', error);
              baseMessage.file_expired = true;
              baseMessage.error_message = '文件处理失败';
            }
          } else {
            // 文件已下载或已过期
            baseMessage.file_expired = true;
            baseMessage.error_message = msg.downloaded ? '文件已下载' : '下载链接已过期';
          }
        } else {
          // 文件信息不完整
          baseMessage.file_expired = true;
          baseMessage.error_message = '文件信息缺失';
        }
      }

      return baseMessage;
    }));

    return {
      success: true,
      messages: processedMessages,
      has_more: hasMore,
      next_since_id: nextSinceId
    };
  } catch (error) {
    console.error('获取增量消息失败:', error);
    return {
      success: false,
      message: error.message,
      messages: [],
      has_more: false,
      next_since_id: sinceId // 返回原始的since_id
    };
  }
};

/**
 * 更新设备同步状态
 * @param {string} userUuid 用户UUID
 * @param {string} deviceId 设备ID
 * @param {number} lastSyncedId 最后同步的消息ID
 * @returns {Promise<Object>} 更新结果
 */
const updateDeviceSyncStatus = async (userUuid, deviceId, lastSyncedId) => {
  try {
    const result = await update(
      'user_device_bindings',
      {
        last_synced_id: lastSyncedId,
        last_active_at: new Date()
      },
      {
        user_uuid: userUuid,
        device_id: deviceId
      }
    );
    
    return result;
  } catch (error) {
    console.error('更新设备同步状态失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 注册设备
 * @param {Object} deviceInfo 设备信息
 * @returns {Promise<Object>} 注册结果
 */
const registerDevice = async (deviceInfo) => {
  try {
    // 验证设备信息
    const validation = deviceManager.validateDeviceInfo(deviceInfo);
    if (!validation.valid) {
      return {
        success: false,
        message: `设备信息验证失败: ${validation.errors.join(', ')}`
      };
    }
    
    // 标准化设备信息
    const normalizedInfo = deviceManager.normalizeDeviceInfo(deviceInfo);
    
    const {
      user_uuid,
      device_id,
      device_name,
      platform,
      platform_version,
      app_version,
      device_model,
      push_token,
      push_provider,
      device_fingerprint
    } = normalizedInfo;
    
    // 首先确保用户记录存在
    const existingUser = await getOne(
      'SELECT user_uuid FROM app_users WHERE user_uuid = ?',
      [user_uuid]
    );

    if (!existingUser) {
      // 创建用户记录
      console.log('创建新用户记录:', user_uuid);
      const userResult = await insert('app_users', {
        user_uuid,
        first_device_id: device_id,
        device_count: 1
      });

      if (!userResult.success) {
        console.error('创建用户记录失败:', userResult.error);
        return {
          success: false,
          message: '创建用户记录失败'
        };
      }
    }

    // 检查设备是否已存在
    const existingDevice = await getOne(
      'SELECT id, device_status FROM user_device_bindings WHERE user_uuid = ? AND device_id = ?',
      [user_uuid, device_id]
    );
    
    if (existingDevice) {
      // 更新现有设备 - 确保undefined转换为null
      const updateData = {
        device_name: device_name || null,
        platform: platform || null,
        platform_version: platform_version || null,
        app_version: app_version || null,
        device_model: device_model || null,
        push_token: push_token || null,
        push_provider: push_provider || null,
        device_fingerprint: device_fingerprint || null,
        last_active_at: new Date(),
        device_status: 'active'
      };

      console.log('更新设备数据:', updateData);

      const result = await update(
        'user_device_bindings',
        updateData,
        {
          user_uuid,
          device_id
        }
      );
      
      if (result.success) {
        return {
          success: true,
          device: {
            id: existingDevice.id,
            user_uuid,
            device_id,
            device_name,
            platform,
            platform_version,
            app_version,
            device_model,
            push_token,
            push_provider,
            device_fingerprint,
            device_status: 'active',
            sync_strategy: deviceManager.getSyncStrategy(platform)
          }
        };
      }
    } else {
      // 创建新设备 - 确保undefined转换为null
      const insertData = {
        user_uuid,
        device_id,
        device_name: device_name || null,
        platform: platform || null,
        platform_version: platform_version || null,
        app_version: app_version || null,
        device_model: device_model || null,
        push_token: push_token || null,
        push_provider: push_provider || null,
        device_fingerprint: device_fingerprint || null,
        device_status: 'active'
      };

      console.log('插入设备数据:', insertData);

      const result = await insert('user_device_bindings', insertData);
      
      if (result.success) {
        console.log('设备注册成功:', result.insertId);

        // 更新用户的设备计数
        await query(
          'UPDATE app_users SET device_count = (SELECT COUNT(*) FROM user_device_bindings WHERE user_uuid = ?) WHERE user_uuid = ?',
          [user_uuid, user_uuid]
        );

        return {
          success: true,
          device: {
            id: result.insertId,
            user_uuid,
            device_id,
            device_name,
            platform,
            platform_version,
            app_version,
            device_model,
            push_token,
            push_provider,
            device_fingerprint,
            device_status: 'active',
            sync_strategy: deviceManager.getSyncStrategy(platform)
          }
        };
      } else {
        console.error('设备注册失败:', result.error);
        return {
          success: false,
          message: `设备注册失败: ${result.error}`
        };
      }
    }
    
    console.error('设备注册流程异常结束');
    return {
      success: false,
      message: '设备注册流程异常结束'
    };
  } catch (error) {
    console.error('注册设备失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 推送消息到设备（通过JPush）
 * @param {string} userUuid 用户UUID
 * @param {number} messageId 消息ID
 * @returns {Promise<Object>} 推送结果
 */
const pushMessageToDevices = async (userUuid, messageId) => {
  try {
    // 获取用户所有活跃设备
    const devices = await query(`
      SELECT device_id, platform, push_token, push_provider
      FROM user_device_bindings
      WHERE user_uuid = ? AND device_status = 'active' AND push_token IS NOT NULL
    `, [userUuid]);
    
    if (devices.length === 0) {
      return {
        success: false,
        message: '用户没有可推送的设备'
      };
    }

    // 这里应该调用JPush API进行推送
    // 暂时返回模拟结果
    return {
      success: true,
      pushed_devices: devices.length,
      message: '推送成功'
    };
  } catch (error) {
    console.error('推送消息失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 推送下载指令到设备
 * @param {string} userUuid 用户UUID
 * @param {Object} downloadInstruction 下载指令
 * @returns {Promise<Object>} 推送结果
 */
const pushDownloadInstructionToDevices = async (userUuid, downloadInstruction) => {
  try {
    // 获取用户所有活跃设备
    const devices = await query(`
      SELECT device_id, platform, push_token, push_provider
      FROM user_device_bindings
      WHERE user_uuid = ? AND device_status = 'active' AND push_token IS NOT NULL
    `, [userUuid]);
    
    if (devices.length === 0) {
      return {
        success: false,
        message: '用户没有可推送的设备'
      };
    }

    // 这里应该调用JPush API进行推送
    // 暂时返回模拟结果
    return {
      success: true,
      pushed_devices: devices.length,
      message: '下载指令推送成功'
    };
  } catch (error) {
    console.error('推送下载指令失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};





module.exports = {
  isUserWechatBound,
  getUserBindingStatus,
  getBindingByUserUuid,
  getBindingByExternalUserId,
  createOrUpdateBinding,
  deleteBinding,
  saveMessageMetadata,
  markFileAsDownloaded,
  getIncrementalMessages,
  updateDeviceSyncStatus,
  registerDevice,
  pushMessageToDevices,
  pushDownloadInstructionToDevices
}; 