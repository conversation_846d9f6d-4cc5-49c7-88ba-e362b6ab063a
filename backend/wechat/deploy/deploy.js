#!/usr/bin/env node

/**
 * 公职猫微信转发服务 - 高级部署管理工具
 * 支持多环境部署、健康检查、自动回滚等功能
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const https = require('https');

// 颜色定义
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

class DeploymentManager {
    constructor() {
        this.scriptDir = __dirname;
        this.projectDir = path.dirname(this.scriptDir);
        this.configFile = path.join(this.scriptDir, 'deploy-config.json');
        this.config = this.loadConfig();
        this.startTime = Date.now();
    }

    // 加载配置文件
    loadConfig() {
        try {
            const configData = fs.readFileSync(this.configFile, 'utf8');
            return JSON.parse(configData);
        } catch (error) {
            this.error(`无法加载配置文件: ${error.message}`);
            process.exit(1);
        }
    }

    // 日志方法
    log(message, color = 'green') {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`${colors[color]}[${timestamp}]${colors.reset} ${message}`);
    }

    error(message) {
        console.error(`${colors.red}[ERROR]${colors.reset} ${message}`);
    }

    warning(message) {
        console.log(`${colors.yellow}[WARNING]${colors.reset} ${message}`);
    }

    info(message) {
        console.log(`${colors.blue}[INFO]${colors.reset} ${message}`);
    }

    success(message) {
        console.log(`${colors.green}[SUCCESS]${colors.reset} ${message}`);
    }

    // 执行命令
    async execCommand(command, options = {}) {
        return new Promise((resolve, reject) => {
            try {
                const result = execSync(command, {
                    encoding: 'utf8',
                    stdio: options.silent ? 'pipe' : 'inherit',
                    ...options
                });
                resolve(result);
            } catch (error) {
                reject(error);
            }
        });
    }

    // SSH命令执行
    async sshCommand(environment, command, options = {}) {
        const env = this.config.environments[environment];
        if (!env) {
            throw new Error(`未找到环境配置: ${environment}`);
        }

        const sshCmd = `ssh ${env.server.user}@${env.server.host} "${command}"`;
        return this.execCommand(sshCmd, options);
    }

    // 检查环境
    async checkEnvironment(environment) {
        this.log(`🔍 检查环境: ${environment}`, 'blue');
        
        const env = this.config.environments[environment];
        if (!env) {
            throw new Error(`未找到环境配置: ${environment}`);
        }

        // 检查SSH连接
        try {
            await this.sshCommand(environment, 'echo "连接成功"', { silent: true });
            this.log('✅ SSH连接正常');
        } catch (error) {
            throw new Error(`SSH连接失败: ${error.message}`);
        }

        // 检查Node.js版本
        try {
            const nodeVersion = await this.sshCommand(environment, 'node -v', { silent: true });
            const version = parseInt(nodeVersion.trim().substring(1));
            if (version < env.service.nodeVersion) {
                throw new Error(`Node.js版本过低: ${nodeVersion.trim()}, 需要: ${env.service.nodeVersion}+`);
            }
            this.log(`✅ Node.js版本: ${nodeVersion.trim()}`);
        } catch (error) {
            throw new Error(`Node.js检查失败: ${error.message}`);
        }

        // 检查PM2
        if (env.service.processManager === 'pm2') {
            try {
                await this.sshCommand(environment, 'pm2 -v', { silent: true });
                this.log('✅ PM2已安装');
            } catch (error) {
                this.warning('PM2未安装，将使用后台进程方式启动');
            }
        }

        this.log('✅ 环境检查通过');
    }

    // 创建备份
    async createBackup(environment) {
        this.log('💾 创建服务备份...', 'blue');
        
        const env = this.config.environments[environment];
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupName = `wechat-service-${timestamp}.tar.gz`;

        const backupCommand = `
            mkdir -p ${env.server.backupPath} &&
            if [ -d '${env.server.deployPath}' ]; then
                cd '${path.dirname(env.server.deployPath)}' &&
                tar -czf '${env.server.backupPath}/${backupName}' '${path.basename(env.server.deployPath)}' 2>/dev/null || true &&
                echo "备份已创建: ${env.server.backupPath}/${backupName}" &&
                cd '${env.server.backupPath}' &&
                ls -t wechat-service-*.tar.gz 2>/dev/null | tail -n +${this.config.deployment.backupRetention + 1} | xargs rm -f 2>/dev/null || true
            fi
        `;

        await this.sshCommand(environment, backupCommand);
        this.log('✅ 备份创建完成');
        return backupName;
    }

    // 停止服务
    async stopService(environment) {
        this.log('⏹️  停止现有服务...', 'blue');
        
        const env = this.config.environments[environment];
        
        const stopCommand = `
            if command -v pm2 >/dev/null 2>&1; then
                pm2 stop '${env.service.name}' 2>/dev/null || true
                pm2 delete '${env.service.name}' 2>/dev/null || true
            fi
            
            if command -v docker >/dev/null 2>&1; then
                docker stop '${env.service.name}' 2>/dev/null || true
                docker rm '${env.service.name}' 2>/dev/null || true
            fi
            
            pkill -f 'node.*${env.service.startupScript}' 2>/dev/null || true
            
            echo '服务已停止'
        `;

        await this.sshCommand(environment, stopCommand);
        this.log('✅ 服务停止完成');
    }

    // 同步代码
    async syncCode(environment) {
        this.log('📦 同步项目代码...', 'blue');
        
        const env = this.config.environments[environment];
        
        // 创建部署目录
        await this.sshCommand(environment, `mkdir -p ${env.server.deployPath}`);
        
        // 构建排除模式
        const excludePatterns = this.config.deployment.excludePatterns
            .map(pattern => `--exclude=${pattern}`)
            .join(' ');
        
        // 同步代码
        const rsyncCommand = `rsync -avz --delete ${excludePatterns} ${this.projectDir}/ ${env.server.user}@${env.server.host}:${env.server.deployPath}/`;
        
        await this.execCommand(rsyncCommand);
        this.log('✅ 代码同步完成');
    }

    // 安装依赖
    async installDependencies(environment) {
        this.log('📚 安装项目依赖...', 'blue');
        
        const env = this.config.environments[environment];
        
        const installCommand = `
            cd '${env.server.deployPath}' &&
            if command -v yarn >/dev/null 2>&1; then
                yarn install --production --frozen-lockfile
            else
                npm install --production --no-package-lock
            fi &&
            echo '依赖安装完成'
        `;

        await this.sshCommand(environment, installCommand);
        this.log('✅ 依赖安装完成');
    }

    // 启动服务
    async startService(environment) {
        this.log('🚀 启动服务...', 'blue');
        
        const env = this.config.environments[environment];
        
        // 语法检查
        await this.sshCommand(environment, `cd '${env.server.deployPath}' && node -c ${env.service.startupScript}`);
        
        let startCommand;
        if (env.service.processManager === 'pm2') {
            startCommand = `
                cd '${env.server.deployPath}' &&
                pm2 start ${env.service.startupScript} --name '${env.service.name}' --env production &&
                pm2 save
            `;
        } else {
            startCommand = `
                cd '${env.server.deployPath}' &&
                mkdir -p logs &&
                nohup node ${env.service.startupScript} > logs/service.log 2>&1 &
                echo $! > service.pid
            `;
        }

        await this.sshCommand(environment, startCommand);
        this.log('✅ 服务启动完成');
    }

    // 健康检查
    async healthCheck(environment) {
        this.log('🏥 执行健康检查...', 'blue');
        
        const env = this.config.environments[environment];
        const protocol = env.ssl?.enabled ? 'https' : 'http';
        const healthUrl = `${protocol}://${env.server.host}${env.service.healthCheckUrl}`;
        
        const maxAttempts = this.config.deployment.healthCheckRetries;
        const timeout = this.config.deployment.healthCheckTimeout * 1000;
        
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            this.info(`健康检查尝试 ${attempt}/${maxAttempts}...`);
            
            try {
                await this.httpRequest(healthUrl, { timeout: 5000 });
                this.log('✅ 服务健康检查通过');
                return true;
            } catch (error) {
                if (attempt === maxAttempts) {
                    throw new Error(`健康检查失败: ${error.message}`);
                }
                await this.sleep(2000);
            }
        }
        
        return false;
    }

    // HTTP请求
    httpRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            const request = https.get(url, { timeout: options.timeout || 10000 }, (response) => {
                if (response.statusCode === 200) {
                    resolve(response);
                } else {
                    reject(new Error(`HTTP ${response.statusCode}`));
                }
            });
            
            request.on('error', reject);
            request.on('timeout', () => {
                request.destroy();
                reject(new Error('请求超时'));
            });
        });
    }

    // 睡眠函数
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 显示部署信息
    showDeploymentInfo(environment) {
        const env = this.config.environments[environment];
        const protocol = env.ssl?.enabled ? 'https' : 'http';
        const duration = Math.round((Date.now() - this.startTime) / 1000);
        
        console.log('');
        this.success(`🎉 ${this.config.project.name} 部署成功！`);
        console.log('');
        this.info(`📡 服务地址: ${protocol}://${env.server.host}`);
        this.info(`🏥 健康检查: ${protocol}://${env.server.host}${env.service.healthCheckUrl}`);
        this.info(`🔗 Webhook地址: ${protocol}://${env.server.host}/api/wechat/webhook`);
        this.info(`⏱️  部署耗时: ${duration}秒`);
        console.log('');
        this.log('📝 后续步骤:', 'yellow');
        console.log('1. 在企业微信管理后台配置Webhook URL');
        console.log('2. 测试微信消息转发功能');
        console.log('3. 检查服务日志确认运行状态');
        console.log('');
    }

    // 主部署流程
    async deploy(environment = 'production') {
        try {
            console.log('');
            this.log(`🚀 开始部署 ${this.config.project.name}`, 'cyan');
            this.info(`目标环境: ${environment}`);
            this.info(`项目版本: ${this.config.project.version}`);
            console.log('');

            await this.checkEnvironment(environment);
            const backupName = await this.createBackup(environment);
            await this.stopService(environment);
            await this.syncCode(environment);
            await this.installDependencies(environment);
            await this.startService(environment);
            await this.healthCheck(environment);
            
            this.showDeploymentInfo(environment);
            this.log('🎉 部署完成！', 'green');
            
        } catch (error) {
            this.error(`部署失败: ${error.message}`);
            this.warning('建议检查日志并考虑回滚到上一个版本');
            process.exit(1);
        }
    }

    // 回滚功能
    async rollback(environment = 'production') {
        this.log(`🔄 开始回滚服务...`, 'yellow');
        
        const env = this.config.environments[environment];
        
        // 获取最新备份
        const getLatestBackup = `
            cd '${env.server.backupPath}' &&
            ls -t wechat-service-*.tar.gz 2>/dev/null | head -n 1
        `;
        
        try {
            const latestBackup = (await this.sshCommand(environment, getLatestBackup, { silent: true })).trim();
            
            if (!latestBackup) {
                throw new Error('未找到可用的备份文件');
            }
            
            this.info(`使用备份文件: ${latestBackup}`);
            
            // 停止当前服务
            await this.stopService(environment);
            
            // 恢复备份
            const restoreCommand = `
                cd '${path.dirname(env.server.deployPath)}' &&
                rm -rf '${env.server.deployPath}' &&
                tar -xzf '${env.server.backupPath}/${latestBackup}' &&
                echo '备份恢复完成'
            `;
            
            await this.sshCommand(environment, restoreCommand);
            
            // 重启服务
            await this.startService(environment);
            await this.healthCheck(environment);
            
            this.success('🎉 回滚完成！');
            
        } catch (error) {
            this.error(`回滚失败: ${error.message}`);
            process.exit(1);
        }
    }

    // 显示状态
    async status(environment = 'production') {
        const env = this.config.environments[environment];
        
        this.log(`📊 检查服务状态: ${environment}`, 'blue');
        
        try {
            if (env.service.processManager === 'pm2') {
                const pm2Status = await this.sshCommand(environment, `pm2 show ${env.service.name}`, { silent: true });
                console.log(pm2Status);
            } else {
                const processStatus = await this.sshCommand(environment, `ps aux | grep "${env.service.startupScript}" | grep -v grep`, { silent: true });
                console.log(processStatus || '服务未运行');
            }
            
            // 健康检查
            await this.healthCheck(environment);
            
        } catch (error) {
            this.error(`状态检查失败: ${error.message}`);
        }
    }
}

// 命令行接口
function main() {
    const args = process.argv.slice(2);
    const command = args[0] || 'deploy';
    const environment = args[1] || 'production';
    
    const manager = new DeploymentManager();
    
    switch (command) {
        case 'deploy':
            manager.deploy(environment);
            break;
        case 'rollback':
            manager.rollback(environment);
            break;
        case 'status':
            manager.status(environment);
            break;
        case 'health':
            manager.healthCheck(environment);
            break;
        default:
            console.log('用法: node deploy.js [deploy|rollback|status|health] [environment]');
            console.log('环境: production, staging');
            process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = DeploymentManager;
