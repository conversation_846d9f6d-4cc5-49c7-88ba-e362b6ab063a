#!/bin/bash

# 公职猫微信转发服务 - 部署系统测试脚本
# 用于验证自动化部署系统的各个组件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 测试结果
TESTS_PASSED=0
TESTS_FAILED=0
TEST_RESULTS=()

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="${3:-0}"
    
    info "🧪 测试: $test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        local result=$?
        if [ $result -eq $expected_result ]; then
            success "✅ $test_name - 通过"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            TEST_RESULTS+=("✅ $test_name")
        else
            error "❌ $test_name - 失败 (退出码: $result, 期望: $expected_result)"
            TESTS_FAILED=$((TESTS_FAILED + 1))
            TEST_RESULTS+=("❌ $test_name")
        fi
    else
        error "❌ $test_name - 执行失败"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        TEST_RESULTS+=("❌ $test_name")
    fi
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                 公职猫微信转发服务部署系统测试                 ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  🧪 验证部署脚本  🔍 检查配置文件  📊 测试监控工具            ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 测试文件存在性
test_files_exist() {
    log "📁 测试部署文件完整性..."
    
    local required_files=(
        "auto-deploy.sh"
        "deploy.js"
        "setup-env.sh"
        "env-vault.js"
        "monitor.js"
        "auto-rollback.sh"
        "one-click-deploy.sh"
        "deploy-config.json"
        "README.md"
    )
    
    for file in "${required_files[@]}"; do
        run_test "文件存在: $file" "[ -f '$SCRIPT_DIR/$file' ]"
    done
    
    # 测试项目文件
    run_test "项目文件存在: package.json" "[ -f '$PROJECT_DIR/package.json' ]"
    run_test "项目文件存在: index.js" "[ -f '$PROJECT_DIR/index.js' ]"
}

# 测试脚本权限
test_script_permissions() {
    log "🔐 测试脚本执行权限..."
    
    local script_files=(
        "auto-deploy.sh"
        "setup-env.sh"
        "auto-rollback.sh"
        "one-click-deploy.sh"
        "deploy.js"
        "env-vault.js"
        "monitor.js"
    )
    
    for script in "${script_files[@]}"; do
        run_test "脚本可执行: $script" "[ -x '$SCRIPT_DIR/$script' ]"
    done
}

# 测试配置文件
test_config_files() {
    log "⚙️  测试配置文件格式..."
    
    # 测试JSON配置文件
    run_test "JSON格式: deploy-config.json" "node -e 'JSON.parse(require(\"fs\").readFileSync(\"$SCRIPT_DIR/deploy-config.json\", \"utf8\"))'"
    
    # 测试PM2配置文件
    if [ -f "$PROJECT_DIR/ecosystem.config.js" ]; then
        run_test "PM2配置语法" "node -c '$PROJECT_DIR/ecosystem.config.js'"
    fi
    
    # 测试环境变量模板
    if [ -f "$PROJECT_DIR/config/env.template" ]; then
        run_test "环境变量模板存在" "[ -f '$PROJECT_DIR/config/env.template' ]"
    fi
}

# 测试Node.js脚本语法
test_nodejs_syntax() {
    log "🔍 测试Node.js脚本语法..."
    
    local nodejs_scripts=(
        "deploy.js"
        "env-vault.js"
        "monitor.js"
    )
    
    for script in "${nodejs_scripts[@]}"; do
        run_test "Node.js语法: $script" "node -c '$SCRIPT_DIR/$script'"
    done
    
    # 测试主项目文件
    run_test "主程序语法: index.js" "node -c '$PROJECT_DIR/index.js'"
}

# 测试依赖工具
test_dependencies() {
    log "🔧 测试系统依赖..."
    
    local required_tools=(
        "node"
        "npm"
        "ssh"
        "rsync"
        "curl"
        "tar"
        "gzip"
    )
    
    for tool in "${required_tools[@]}"; do
        run_test "工具可用: $tool" "command -v $tool"
    done
    
    # 测试Node.js版本
    run_test "Node.js版本 >= 18" "[ \$(node -v | cut -d'v' -f2 | cut -d'.' -f1) -ge 18 ]"
}

# 测试环境变量工具
test_env_vault() {
    log "🔐 测试环境变量加密工具..."
    
    # 创建测试环境变量文件
    local test_env_file="/tmp/test-env-$$"
    cat > "$test_env_file" << EOF
NODE_ENV=test
TEST_SECRET=test_secret_value
TEST_PASSWORD=test_password_123
EOF
    
    # 测试加密功能
    run_test "环境变量工具帮助" "node '$SCRIPT_DIR/env-vault.js' help"
    run_test "环境变量验证" "node '$SCRIPT_DIR/env-vault.js' validate '$test_env_file'" 1
    run_test "生成示例配置" "node '$SCRIPT_DIR/env-vault.js' sample"
    
    # 清理测试文件
    rm -f "$test_env_file" 2>/dev/null || true
}

# 测试监控工具
test_monitor() {
    log "📊 测试监控工具..."
    
    # 测试监控脚本帮助
    run_test "监控工具帮助" "node '$SCRIPT_DIR/monitor.js' 2>&1 | grep -q '用法'"
    
    # 测试配置加载
    run_test "监控配置加载" "node -e 'const Monitor = require(\"$SCRIPT_DIR/monitor.js\"); new Monitor();'"
}

# 测试部署脚本
test_deploy_scripts() {
    log "🚀 测试部署脚本..."
    
    # 测试部署脚本帮助
    run_test "部署脚本帮助" "node '$SCRIPT_DIR/deploy.js' 2>&1 | grep -q '用法'"
    
    # 测试自动部署脚本语法
    run_test "自动部署脚本语法" "bash -n '$SCRIPT_DIR/auto-deploy.sh'"
    run_test "一键部署脚本语法" "bash -n '$SCRIPT_DIR/one-click-deploy.sh'"
    run_test "自动回滚脚本语法" "bash -n '$SCRIPT_DIR/auto-rollback.sh'"
    run_test "环境配置脚本语法" "bash -n '$SCRIPT_DIR/setup-env.sh'"
}

# 测试Docker配置
test_docker_config() {
    log "🐳 测试Docker配置..."
    
    if [ -f "$PROJECT_DIR/Dockerfile" ]; then
        run_test "Dockerfile存在" "[ -f '$PROJECT_DIR/Dockerfile' ]"
    fi
    
    if [ -f "$PROJECT_DIR/docker-compose.yml" ]; then
        run_test "Docker Compose配置存在" "[ -f '$PROJECT_DIR/docker-compose.yml' ]"
        
        # 如果有docker命令，测试配置语法
        if command -v docker >/dev/null 2>&1; then
            run_test "Docker Compose配置语法" "cd '$PROJECT_DIR' && docker-compose config >/dev/null"
        fi
    fi
}

# 生成测试报告
generate_test_report() {
    local total_tests=$((TESTS_PASSED + TESTS_FAILED))
    local success_rate=0
    
    if [ $total_tests -gt 0 ]; then
        success_rate=$(( (TESTS_PASSED * 100) / total_tests ))
    fi
    
    echo ""
    echo -e "${CYAN}📋 测试报告${NC}"
    echo "=================================="
    echo "总测试数: $total_tests"
    echo "通过: $TESTS_PASSED"
    echo "失败: $TESTS_FAILED"
    echo "成功率: $success_rate%"
    echo ""
    
    if [ $TESTS_FAILED -eq 0 ]; then
        success "🎉 所有测试通过！部署系统就绪。"
    else
        warning "⚠️  有 $TESTS_FAILED 个测试失败，请检查以下问题："
        echo ""
        for result in "${TEST_RESULTS[@]}"; do
            if [[ $result == ❌* ]]; then
                echo "  $result"
            fi
        done
    fi
    
    echo ""
    echo -e "${BLUE}📝 详细结果:${NC}"
    for result in "${TEST_RESULTS[@]}"; do
        echo "  $result"
    done
    
    # 保存测试报告
    local report_file="$SCRIPT_DIR/test-report-$(date +%Y%m%d_%H%M%S).json"
    cat > "$report_file" << EOF
{
  "test_report": {
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "total_tests": $total_tests,
    "passed": $TESTS_PASSED,
    "failed": $TESTS_FAILED,
    "success_rate": $success_rate,
    "results": $(printf '%s\n' "${TEST_RESULTS[@]}" | jq -R . | jq -s .)
  }
}
EOF
    
    info "测试报告已保存: $report_file"
}

# 主函数
main() {
    show_banner
    
    log "开始部署系统测试..."
    echo ""
    
    # 执行所有测试
    test_files_exist
    test_script_permissions
    test_config_files
    test_nodejs_syntax
    test_dependencies
    test_env_vault
    test_monitor
    test_deploy_scripts
    test_docker_config
    
    # 生成测试报告
    generate_test_report
    
    # 返回适当的退出码
    if [ $TESTS_FAILED -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
