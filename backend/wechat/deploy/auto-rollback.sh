#!/bin/bash

# 公职猫微信转发服务 - 自动回滚脚本
# 在检测到服务异常时自动回滚到上一个稳定版本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-production}"
MAX_ROLLBACK_ATTEMPTS=3
HEALTH_CHECK_TIMEOUT=60
HEALTH_CHECK_INTERVAL=5

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 检查服务健康状态
check_service_health() {
    local environment=$1
    local max_attempts=${2:-12}
    local interval=${3:-5}
    
    info "检查服务健康状态..."
    
    for ((i=1; i<=max_attempts; i++)); do
        log "健康检查尝试 $i/$max_attempts"
        
        if node "$SCRIPT_DIR/monitor.js" health "$environment" >/dev/null 2>&1; then
            log "✅ 服务健康检查通过"
            return 0
        fi
        
        if [ $i -lt $max_attempts ]; then
            sleep $interval
        fi
    done
    
    error "❌ 服务健康检查失败"
    return 1
}

# 获取服务状态
get_service_status() {
    local environment=$1
    
    info "获取服务状态..."
    
    local status_output
    if status_output=$(node "$SCRIPT_DIR/monitor.js" status "$environment" 2>/dev/null); then
        echo "$status_output"
        return 0
    else
        error "无法获取服务状态"
        return 1
    fi
}

# 检查是否需要回滚
should_rollback() {
    local environment=$1
    
    info "评估是否需要回滚..."
    
    # 检查健康状态
    if ! check_service_health "$environment" 3 2; then
        warning "健康检查失败，建议回滚"
        return 0
    fi
    
    # 检查服务状态
    local status_json
    if status_json=$(get_service_status "$environment"); then
        # 解析状态信息
        local process_status=$(echo "$status_json" | node -e "
            const data = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
            console.log(data.process ? data.process.status : 'unknown');
        " 2>/dev/null || echo "unknown")
        
        local port_status=$(echo "$status_json" | node -e "
            const data = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
            console.log(data.port || 'unknown');
        " 2>/dev/null || echo "unknown")
        
        if [ "$process_status" != "online" ] || [ "$port_status" != "listening" ]; then
            warning "服务状态异常，建议回滚"
            warning "进程状态: $process_status, 端口状态: $port_status"
            return 0
        fi
    else
        warning "无法获取服务状态，建议回滚"
        return 0
    fi
    
    info "服务状态正常，无需回滚"
    return 1
}

# 执行回滚
perform_rollback() {
    local environment=$1
    local attempt=${2:-1}
    
    if [ $attempt -gt $MAX_ROLLBACK_ATTEMPTS ]; then
        error "已达到最大回滚尝试次数 ($MAX_ROLLBACK_ATTEMPTS)，停止回滚"
        return 1
    fi
    
    log "🔄 开始执行回滚 (尝试 $attempt/$MAX_ROLLBACK_ATTEMPTS)..."
    
    # 使用部署脚本执行回滚
    if node "$SCRIPT_DIR/deploy.js" rollback "$environment"; then
        log "✅ 回滚执行完成"
        
        # 等待服务启动
        sleep 10
        
        # 验证回滚结果
        if check_service_health "$environment" 12 5; then
            log "🎉 回滚成功，服务已恢复正常"
            return 0
        else
            warning "回滚后服务仍然异常，尝试再次回滚"
            return $(perform_rollback "$environment" $((attempt + 1)))
        fi
    else
        error "回滚执行失败"
        return 1
    fi
}

# 发送告警通知
send_alert() {
    local environment=$1
    local message=$2
    local level=${3:-warning}
    
    info "发送告警通知: $message"
    
    # 记录到日志文件
    local log_file="/var/log/wechat-service/rollback.log"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $message" >> "$log_file" 2>/dev/null || true
    
    # 这里可以集成其他告警方式，如邮件、钉钉、企业微信等
    # 示例：发送到监控系统
    if command -v curl >/dev/null 2>&1; then
        local webhook_url="https://your-monitoring-system.com/webhook"
        local payload="{\"environment\":\"$environment\",\"message\":\"$message\",\"level\":\"$level\",\"timestamp\":\"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"}"
        
        curl -s -X POST "$webhook_url" \
             -H "Content-Type: application/json" \
             -d "$payload" >/dev/null 2>&1 || true
    fi
}

# 生成回滚报告
generate_rollback_report() {
    local environment=$1
    local success=$2
    local start_time=$3
    local end_time=$4
    
    local duration=$((end_time - start_time))
    
    local report_file="$SCRIPT_DIR/rollback-report-$(date +%Y%m%d_%H%M%S).json"
    
    cat > "$report_file" << EOF
{
  "rollback_report": {
    "environment": "$environment",
    "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "success": $success,
    "duration_seconds": $duration,
    "start_time": "$(date -u -d @$start_time +%Y-%m-%dT%H:%M:%SZ)",
    "end_time": "$(date -u -d @$end_time +%Y-%m-%dT%H:%M:%SZ)",
    "trigger": "auto_rollback_script",
    "max_attempts": $MAX_ROLLBACK_ATTEMPTS,
    "health_check_timeout": $HEALTH_CHECK_TIMEOUT
  }
}
EOF
    
    info "回滚报告已生成: $report_file"
}

# 主函数
main() {
    local start_time=$(date +%s)
    
    echo ""
    log "🔄 公职猫微信转发服务自动回滚检查"
    info "目标环境: $ENVIRONMENT"
    info "检查时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    
    # 检查是否需要回滚
    if should_rollback "$ENVIRONMENT"; then
        warning "检测到服务异常，启动自动回滚流程"
        send_alert "$ENVIRONMENT" "检测到服务异常，启动自动回滚" "warning"
        
        if perform_rollback "$ENVIRONMENT"; then
            local end_time=$(date +%s)
            log "🎉 自动回滚成功完成"
            send_alert "$ENVIRONMENT" "自动回滚成功完成" "info"
            generate_rollback_report "$ENVIRONMENT" true $start_time $end_time
        else
            local end_time=$(date +%s)
            error "❌ 自动回滚失败"
            send_alert "$ENVIRONMENT" "自动回滚失败，需要人工介入" "critical"
            generate_rollback_report "$ENVIRONMENT" false $start_time $end_time
            exit 1
        fi
    else
        info "✅ 服务运行正常，无需回滚"
    fi
    
    echo ""
    log "自动回滚检查完成"
}

# 显示帮助信息
show_help() {
    echo "公职猫微信转发服务 - 自动回滚脚本"
    echo ""
    echo "用法: $0 [environment]"
    echo ""
    echo "参数:"
    echo "  environment    目标环境 (默认: production)"
    echo ""
    echo "环境变量:"
    echo "  MAX_ROLLBACK_ATTEMPTS     最大回滚尝试次数 (默认: 3)"
    echo "  HEALTH_CHECK_TIMEOUT      健康检查超时时间 (默认: 60秒)"
    echo "  HEALTH_CHECK_INTERVAL     健康检查间隔 (默认: 5秒)"
    echo ""
    echo "示例:"
    echo "  $0                        # 检查生产环境"
    echo "  $0 staging                # 检查测试环境"
    echo ""
}

# 脚本入口
case "${1:-}" in
    -h|--help|help)
        show_help
        exit 0
        ;;
    *)
        if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
            main "$@"
        fi
        ;;
esac
