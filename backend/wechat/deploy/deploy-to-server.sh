#!/bin/bash

# 公职猫微信转发服务 - 云服务器部署脚本
# 直接在云服务器上执行部署，包含数据库清理和服务启动

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 配置
SERVER_HOST="wechat.api.gongzhimall.com"
SERVER_USER="root"
DEPLOY_PATH="/www/wwwroot/wechat.api.gongzhimall.com"
SERVICE_NAME="gongzhimall-wechat"

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                    公职猫微信转发服务                          ║${NC}"
    echo -e "${CYAN}║                   云服务器自动化部署                           ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  🌐 远程部署  🗄️  数据库清理  🚀 服务启动  📊 健康检查        ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查SSH连接
check_ssh_connection() {
    log "🔍 检查SSH连接..."
    
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$SERVER_USER@$SERVER_HOST" "echo 'SSH连接成功'" >/dev/null 2>&1; then
        success "SSH连接正常"
    else
        error "无法连接到服务器 $SERVER_HOST，请检查SSH配置"
    fi
}

# 检查服务器环境
check_server_environment() {
    log "🔍 检查服务器环境..."
    
    ssh "$SERVER_USER@$SERVER_HOST" << 'EOF'
        # 检查Node.js
        if command -v node >/dev/null 2>&1; then
            echo "✅ Node.js: $(node -v)"
        else
            echo "❌ Node.js 未安装"
            exit 1
        fi
        
        # 检查npm
        if command -v npm >/dev/null 2>&1; then
            echo "✅ npm: $(npm -v)"
        else
            echo "❌ npm 未安装"
            exit 1
        fi
        
        # 检查PM2
        if command -v pm2 >/dev/null 2>&1; then
            echo "✅ PM2: $(pm2 -v)"
        else
            echo "⚠️  PM2 未安装，将自动安装"
        fi
        
        # 检查数据库连接
        if command -v mysql >/dev/null 2>&1; then
            echo "✅ MySQL客户端可用"
        else
            echo "❌ MySQL客户端未安装"
            exit 1
        fi
EOF
    
    if [ $? -eq 0 ]; then
        success "服务器环境检查通过"
    else
        error "服务器环境检查失败"
    fi
}

# 上传项目文件
upload_project_files() {
    log "📤 上传项目文件到服务器..."
    
    # 创建部署目录
    ssh "$SERVER_USER@$SERVER_HOST" "mkdir -p $DEPLOY_PATH"
    
    # 排除不需要的文件
    local exclude_patterns=(
        "--exclude=node_modules/"
        "--exclude=.git/"
        "--exclude=*.log"
        "--exclude=cache/"
        "--exclude=logs/"
        "--exclude=*.tmp"
        "--exclude=.DS_Store"
        "--exclude=docs/"
        "--exclude=*.md"
        "--exclude=.gitignore"
        "--exclude=deploy/"
    )
    
    # 使用rsync上传文件
    if rsync -avz --delete "${exclude_patterns[@]}" "$PROJECT_DIR/" "$SERVER_USER@$SERVER_HOST:$DEPLOY_PATH/"; then
        success "项目文件上传完成"
    else
        error "项目文件上传失败"
    fi
}

# 在服务器上安装依赖
install_dependencies_on_server() {
    log "📦 在服务器上安装依赖..."
    
    ssh "$SERVER_USER@$SERVER_HOST" << EOF
        cd $DEPLOY_PATH
        
        # 安装PM2（如果未安装）
        if ! command -v pm2 >/dev/null 2>&1; then
            echo "安装PM2..."
            npm install -g pm2
        fi
        
        # 安装项目依赖
        echo "安装项目依赖..."
        npm install --production
        
        echo "依赖安装完成"
EOF
    
    if [ $? -eq 0 ]; then
        success "依赖安装完成"
    else
        error "依赖安装失败"
    fi
}

# 在服务器上清理和初始化数据库
clean_and_init_database_on_server() {
    log "🗄️  在服务器上清理和初始化数据库..."
    
    echo ""
    echo -e "${YELLOW}⚠️  警告: 此操作将删除现有数据库并重新创建${NC}"
    echo "服务器: $SERVER_HOST"
    echo "数据库: gongzhimall_wechat (MySQL分支)"
    echo ""
    
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info "数据库清理已取消"
        return 0
    fi
    
    ssh "$SERVER_USER@$SERVER_HOST" << EOF
        cd $DEPLOY_PATH
        
        echo "执行数据库清理和初始化..."
        if node scripts/clean-and-init-database.js; then
            echo "✅ 数据库初始化成功"
        else
            echo "❌ 数据库初始化失败"
            exit 1
        fi
EOF
    
    if [ $? -eq 0 ]; then
        success "数据库清理和初始化完成"
    else
        error "数据库初始化失败"
    fi
}

# 在服务器上启动服务
start_service_on_server() {
    log "🚀 在服务器上启动微信转发服务..."
    
    ssh "$SERVER_USER@$SERVER_HOST" << EOF
        cd $DEPLOY_PATH
        
        # 停止现有服务
        pm2 stop $SERVICE_NAME 2>/dev/null || true
        pm2 delete $SERVICE_NAME 2>/dev/null || true
        
        # 启动服务
        if [ -f "ecosystem.config.js" ]; then
            pm2 start ecosystem.config.js --env production
        else
            pm2 start index.js --name $SERVICE_NAME --env production
        fi
        
        # 保存PM2配置
        pm2 save
        pm2 startup
        
        echo "服务启动完成"
EOF
    
    if [ $? -eq 0 ]; then
        success "服务启动完成"
    else
        error "服务启动失败"
    fi
}

# 健康检查
health_check_on_server() {
    log "🏥 执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        info "健康检查尝试 $attempt/$max_attempts..."
        
        if ssh "$SERVER_USER@$SERVER_HOST" "curl -f -s http://localhost:3000/health" >/dev/null 2>&1; then
            success "健康检查通过"
            return 0
        fi
        
        sleep 2
        ((attempt++))
    done
    
    error "健康检查失败，服务可能未正常启动"
}

# 显示部署结果
show_deployment_result() {
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    success "🎉 云服务器部署完成！"
    echo ""
    echo -e "${BLUE}📊 部署统计:${NC}"
    echo "  服务器: $SERVER_HOST"
    echo "  部署路径: $DEPLOY_PATH"
    echo "  耗时: ${duration}秒"
    echo "  状态: 成功"
    echo ""
    
    echo -e "${YELLOW}📝 服务信息:${NC}"
    echo "  服务名称: $SERVICE_NAME"
    echo "  运行端口: 3000"
    echo "  数据库: localhost:3306/gongzhimall_wechat (MySQL分支)"
    echo "  健康检查: https://$SERVER_HOST/health"
    echo ""
    
    echo -e "${BLUE}🔧 远程管理命令:${NC}"
    echo "  查看状态: ssh $SERVER_USER@$SERVER_HOST 'pm2 status'"
    echo "  查看日志: ssh $SERVER_USER@$SERVER_HOST 'pm2 logs $SERVICE_NAME'"
    echo "  重启服务: ssh $SERVER_USER@$SERVER_HOST 'pm2 restart $SERVICE_NAME'"
    echo ""
    
    echo -e "${GREEN}✨ 微信转发服务已在云服务器成功部署！${NC}"
    echo "现在可以开始测试微信转发功能了。"
}

# 错误处理
handle_error() {
    local exit_code=$?
    echo ""
    error "部署过程中发生错误 (退出码: $exit_code)"
    echo ""
    echo -e "${YELLOW}🔧 故障排除建议:${NC}"
    echo "1. 检查SSH连接和服务器访问权限"
    echo "2. 验证服务器环境和依赖"
    echo "3. 查看服务器上的详细错误日志"
    echo "4. 检查数据库连接和权限"
    echo ""
    exit $exit_code
}

# 主函数
main() {
    # 设置错误处理
    trap 'handle_error' ERR
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 显示横幅
    show_banner
    
    # 确认部署
    echo -e "${YELLOW}📋 云服务器部署确认:${NC}"
    echo "  项目: 公职猫微信转发服务"
    echo "  目标服务器: $SERVER_HOST"
    echo "  部署路径: $DEPLOY_PATH"
    echo "  数据库: 将清理并重新初始化 (MySQL分支)"
    echo "  时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    
    read -p "确认开始云服务器部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info "部署已取消"
        exit 0
    fi
    
    # 执行部署流程
    check_ssh_connection
    check_server_environment
    upload_project_files
    install_dependencies_on_server
    clean_and_init_database_on_server
    start_service_on_server
    health_check_on_server
    show_deployment_result
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
