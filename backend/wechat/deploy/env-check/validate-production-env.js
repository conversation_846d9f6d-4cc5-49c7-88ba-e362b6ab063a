#!/usr/bin/env node

/**
 * 生产环境变量验证脚本
 * 确保所有必需的环境变量都已正确配置
 */

const fs = require('fs');
const path = require('path');

// 必需的环境变量配置
const REQUIRED_ENV_VARS = {
    // 服务器配置
    'PORT': {
        required: true,
        type: 'number',
        default: '3000',
        description: '服务器端口'
    },
    'SERVER_DOMAIN': {
        required: true,
        type: 'string',
        example: 'wechat.api.gongzhimall.com',
        description: '服务器域名'
    },
    'NODE_ENV': {
        required: true,
        type: 'string',
        default: 'production',
        description: '运行环境'
    },
    
    // 数据库配置
    'MYSQL_HOST': {
        required: true,
        type: 'string',
        default: 'localhost',
        description: 'MySQL主机地址'
    },
    'MYSQL_PORT': {
        required: true,
        type: 'number',
        default: '3306',
        description: 'MySQL端口'
    },
    'MYSQL_USER': {
        required: true,
        type: 'string',
        example: 'wechat_user',
        description: 'MySQL用户名'
    },
    'MYSQL_PASSWORD': {
        required: true,
        type: 'string',
        sensitive: true,
        description: 'MySQL密码'
    },
    'MYSQL_DATABASE': {
        required: true,
        type: 'string',
        default: 'gongzhimall_wechat',
        description: 'MySQL数据库名'
    },
    
    // 企业微信配置
    'WECHAT_CORP_ID': {
        required: true,
        type: 'string',
        sensitive: true,
        description: '企业微信Corp ID'
    },
    'WECHAT_CORP_SECRET': {
        required: true,
        type: 'string',
        sensitive: true,
        description: '企业微信Corp Secret'
    },
    'WECHAT_AGENT_ID': {
        required: true,
        type: 'string',
        description: '企业微信Agent ID'
    },
    'WECHAT_TOKEN': {
        required: true,
        type: 'string',
        sensitive: true,
        description: '企业微信Token'
    },
    'WECHAT_ENCODING_AES_KEY': {
        required: true,
        type: 'string',
        sensitive: true,
        description: '企业微信EncodingAESKey'
    },
    'WECHAT_DEFAULT_OPEN_KFID': {
        required: true,
        type: 'string',
        description: '默认客服账号ID'
    },
    
    // 推送服务配置
    'JPUSH_APP_KEY': {
        required: true,
        type: 'string',
        sensitive: true,
        description: '极光推送App Key'
    },
    'JPUSH_MASTER_SECRET': {
        required: true,
        type: 'string',
        sensitive: true,
        description: '极光推送Master Secret'
    },
    
    // 安全配置
    'TOKEN_SECRET': {
        required: true,
        type: 'string',
        sensitive: true,
        description: 'JWT Token密钥'
    },
    'WECHAT_BINDING_TOKEN_SECRET': {
        required: true,
        type: 'string',
        sensitive: true,
        description: '微信绑定Token密钥'
    },
    'FILE_ENCRYPTION_KEY': {
        required: true,
        type: 'string',
        sensitive: true,
        description: '文件加密密钥'
    }
};

// 日志函数
const log = (message) => {
    console.log(`[${new Date().toISOString()}] ${message}`);
};

const error = (message) => {
    console.error(`[${new Date().toISOString()}] ❌ ${message}`);
};

const success = (message) => {
    console.log(`[${new Date().toISOString()}] ✅ ${message}`);
};

const warning = (message) => {
    console.warn(`[${new Date().toISOString()}] ⚠️  ${message}`);
};

/**
 * 加载环境变量
 */
function loadEnvFile() {
    const envPath = path.join(__dirname, '../.env');
    
    if (!fs.existsSync(envPath)) {
        error('未找到 .env 文件');
        return null;
    }
    
    try {
        const envContent = fs.readFileSync(envPath, 'utf8');
        const envVars = {};
        
        envContent.split('\n').forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#')) {
                const [key, ...valueParts] = line.split('=');
                if (key && valueParts.length > 0) {
                    envVars[key.trim()] = valueParts.join('=').trim();
                }
            }
        });
        
        return envVars;
    } catch (err) {
        error(`读取 .env 文件失败: ${err.message}`);
        return null;
    }
}

/**
 * 验证单个环境变量
 */
function validateEnvVar(key, value, config) {
    const errors = [];
    const warnings = [];
    
    // 检查是否为空
    if (!value || value.trim() === '') {
        if (config.required) {
            errors.push(`${key} 是必需的但未设置`);
        } else if (config.default) {
            warnings.push(`${key} 未设置，将使用默认值: ${config.default}`);
        }
        return { errors, warnings };
    }
    
    // 类型验证
    if (config.type === 'number') {
        if (isNaN(Number(value))) {
            errors.push(`${key} 应该是数字，当前值: ${value}`);
        }
    }
    
    // 长度验证
    if (config.minLength && value.length < config.minLength) {
        errors.push(`${key} 长度不能少于 ${config.minLength} 个字符`);
    }
    
    // 格式验证
    if (key === 'SERVER_DOMAIN' && !value.match(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)) {
        errors.push(`${key} 格式不正确，应该是有效的域名`);
    }
    
    if (key === 'MYSQL_HOST' && value !== 'localhost' && !value.match(/^\d+\.\d+\.\d+\.\d+$/)) {
        warnings.push(`${key} 不是localhost也不是IP地址格式: ${value}`);
    }
    
    return { errors, warnings };
}

/**
 * 验证所有环境变量
 */
function validateAllEnvVars(envVars) {
    log('开始验证环境变量...');
    
    let totalErrors = 0;
    let totalWarnings = 0;
    const results = {};
    
    // 验证必需的环境变量
    for (const [key, config] of Object.entries(REQUIRED_ENV_VARS)) {
        const value = envVars[key];
        const { errors, warnings } = validateEnvVar(key, value, config);
        
        results[key] = {
            value: config.sensitive ? '***' : value,
            errors,
            warnings,
            status: errors.length === 0 ? 'ok' : 'error'
        };
        
        totalErrors += errors.length;
        totalWarnings += warnings.length;
        
        if (errors.length > 0) {
            error(`${key}: ${errors.join(', ')}`);
        } else if (warnings.length > 0) {
            warning(`${key}: ${warnings.join(', ')}`);
        } else {
            success(`${key}: 验证通过`);
        }
    }
    
    // 检查额外的环境变量
    const extraVars = Object.keys(envVars).filter(key => !REQUIRED_ENV_VARS[key]);
    if (extraVars.length > 0) {
        log(`发现额外的环境变量: ${extraVars.join(', ')}`);
    }
    
    return {
        results,
        totalErrors,
        totalWarnings,
        isValid: totalErrors === 0
    };
}

/**
 * 生成环境变量报告
 */
function generateReport(validation) {
    console.log('\n📊 环境变量验证报告');
    console.log('='.repeat(50));
    
    console.log(`总计: ${Object.keys(REQUIRED_ENV_VARS).length} 个必需变量`);
    console.log(`错误: ${validation.totalErrors} 个`);
    console.log(`警告: ${validation.totalWarnings} 个`);
    console.log(`状态: ${validation.isValid ? '✅ 通过' : '❌ 失败'}`);
    
    if (!validation.isValid) {
        console.log('\n❌ 验证失败的变量:');
        for (const [key, result] of Object.entries(validation.results)) {
            if (result.status === 'error') {
                console.log(`  - ${key}: ${result.errors.join(', ')}`);
            }
        }
    }
    
    if (validation.totalWarnings > 0) {
        console.log('\n⚠️  警告信息:');
        for (const [key, result] of Object.entries(validation.results)) {
            if (result.warnings.length > 0) {
                console.log(`  - ${key}: ${result.warnings.join(', ')}`);
            }
        }
    }
    
    console.log('\n💡 配置建议:');
    console.log('1. 确保所有敏感信息（密钥、密码）都已正确设置');
    console.log('2. 数据库地址已更新为 localhost');
    console.log('3. 域名配置正确，SSL证书有效');
    console.log('4. 推送服务配置正确');
}

/**
 * 主函数
 */
function main() {
    console.log('🔍 开始验证生产环境变量配置...\n');
    
    // 加载环境变量
    const envVars = loadEnvFile();
    if (!envVars) {
        process.exit(1);
    }
    
    // 验证环境变量
    const validation = validateAllEnvVars(envVars);
    
    // 生成报告
    generateReport(validation);
    
    if (validation.isValid) {
        console.log('\n🎉 环境变量验证通过！可以继续部署。');
        process.exit(0);
    } else {
        console.log('\n❌ 环境变量验证失败！请修复错误后重试。');
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    validateAllEnvVars,
    loadEnvFile,
    REQUIRED_ENV_VARS
};
