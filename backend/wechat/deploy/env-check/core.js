// core.js
// 通用环境变量校验核心模块，供测试/生产环境检测脚本复用

/**
 * 校验环境变量存在性、类型、格式等
 * @param {Object} envVars - 变量定义（key: {required, type, sensitive, description, ...}）
 * @param {Object} actualEnv - 实际环境变量对象（如process.env或.env文件解析结果）
 * @returns {Object} 校验结果
 */
function validateEnvVars(envVars, actualEnv) {
  const results = {};
  let totalErrors = 0;
  let totalWarnings = 0;

  for (const [key, config] of Object.entries(envVars)) {
    const value = actualEnv[key];
    const errors = [];
    const warnings = [];

    // 必需性校验
    if (config.required && (!value || value.trim() === '')) {
      errors.push('必需变量未设置');
    }
    // 类型校验
    if (value && config.type === 'number' && isNaN(Number(value))) {
      errors.push('应为数字');
    }
    // 长度校验
    if (value && config.minLength && value.length < config.minLength) {
      errors.push(`长度不能少于${config.minLength}`);
    }
    // 格式校验
    if (value && config.pattern && !config.pattern.test(value)) {
      errors.push('格式不正确');
    }
    // 其他自定义校验
    if (config.customValidate && typeof config.customValidate === 'function') {
      const customResult = config.customValidate(value);
      if (customResult !== true) errors.push(customResult);
    }
    // 默认值警告
    if (!value && config.default) {
      warnings.push(`未设置，将使用默认值: ${config.default}`);
    }

    results[key] = {
      value: config.sensitive ? '***' : value,
      errors,
      warnings,
      status: errors.length === 0 ? 'ok' : 'error',
      description: config.description || ''
    };
    totalErrors += errors.length;
    totalWarnings += warnings.length;
  }

  // 检查多余变量
  const extraVars = Object.keys(actualEnv).filter(k => !envVars[k]);

  return {
    results,
    totalErrors,
    totalWarnings,
    isValid: totalErrors === 0,
    extraVars
  };
}

/**
 * 统一输出校验报告
 * @param {Object} validation 校验结果
 * @param {string} envName 环境名
 */
function printValidationReport(validation, envName = '') {
  console.log(`\n📊 ${envName}环境变量校验报告`);
  console.log('='.repeat(50));
  console.log(`总计: ${Object.keys(validation.results).length} 个变量`);
  console.log(`错误: ${validation.totalErrors} 个`);
  console.log(`警告: ${validation.totalWarnings} 个`);
  console.log(`状态: ${validation.isValid ? '✅ 通过' : '❌ 失败'}`);

  if (!validation.isValid) {
    console.log('\n❌ 验证失败的变量:');
    for (const [key, result] of Object.entries(validation.results)) {
      if (result.status === 'error') {
        console.log(`  - ${key}: ${result.errors.join(', ')}`);
      }
    }
  }
  if (validation.totalWarnings > 0) {
    console.log('\n⚠️  警告信息:');
    for (const [key, result] of Object.entries(validation.results)) {
      if (result.warnings.length > 0) {
        console.log(`  - ${key}: ${result.warnings.join(', ')}`);
      }
    }
  }
  if (validation.extraVars && validation.extraVars.length > 0) {
    console.log(`\n🟢 存在未定义的额外变量: ${validation.extraVars.join(', ')}`);
  }
}

module.exports = {
  validateEnvVars,
  printValidationReport
}; 