#!/bin/bash

# 公职猫微信转发服务 - 一键部署脚本
# 集成环境配置、代码部署、服务启动的完整自动化流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 日志函数
log() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 显示横幅
show_banner() {
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                    公职猫微信转发服务                          ║${NC}"
    echo -e "${CYAN}║                     一键自动化部署工具                         ║${NC}"
    echo -e "${CYAN}║                                                              ║${NC}"
    echo -e "${CYAN}║  🚀 支持多环境部署  🔐 安全环境变量管理  📊 健康检查监控        ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 检查依赖
check_dependencies() {
    log "🔍 检查部署依赖..."
    
    local missing_tools=()
    
    # 检查必需工具
    command -v node >/dev/null 2>&1 || missing_tools+=("node")
    command -v npm >/dev/null 2>&1 || missing_tools+=("npm")
    command -v ssh >/dev/null 2>&1 || missing_tools+=("ssh")
    command -v rsync >/dev/null 2>&1 || missing_tools+=("rsync")
    command -v curl >/dev/null 2>&1 || missing_tools+=("curl")
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        error "缺少必需工具: ${missing_tools[*]}
请安装这些工具后重试:
  macOS: brew install ${missing_tools[*]}
  Ubuntu: sudo apt-get install ${missing_tools[*]}
  CentOS: sudo yum install ${missing_tools[*]}"
    fi
    
    # 检查Node.js版本
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        error "Node.js版本过低: $(node -v)，需要18+版本"
    fi
    
    # 检查项目文件
    if [ ! -f "$PROJECT_DIR/package.json" ]; then
        error "未找到package.json文件，请确认在正确的项目目录中运行"
    fi
    
    log "✅ 依赖检查通过"
}

# 选择部署环境
select_environment() {
    echo ""
    echo -e "${YELLOW}📋 请选择部署环境:${NC}"
    echo "1) production  - 生产环境 (wechat.api.gongzhimall.com)"
    echo "2) staging     - 测试环境 (staging.wechat.api.gongzhimall.com)"
    echo "3) custom      - 自定义环境"
    echo ""
    
    while true; do
        read -p "请选择 [1-3]: " choice
        case $choice in
            1)
                ENVIRONMENT="production"
                break
                ;;
            2)
                ENVIRONMENT="staging"
                break
                ;;
            3)
                read -p "请输入自定义环境名称: " ENVIRONMENT
                if [ -n "$ENVIRONMENT" ]; then
                    break
                fi
                ;;
            *)
                echo "无效选择，请重新输入"
                ;;
        esac
    done
    
    info "选择的环境: $ENVIRONMENT"
}

# 检查环境变量配置
check_env_config() {
    log "⚙️  检查环境变量配置..."
    
    local env_file="$PROJECT_DIR/.env"
    local env_vault="$SCRIPT_DIR/.env.vault"
    
    if [ ! -f "$env_file" ] && [ ! -f "$env_vault" ]; then
        warning "未找到环境变量配置文件"
        echo ""
        echo -e "${YELLOW}请选择配置方式:${NC}"
        echo "1) 交互式配置环境变量"
        echo "2) 从加密保险库解密"
        echo "3) 手动配置后继续"
        echo ""
        
        read -p "请选择 [1-3]: " config_choice
        case $config_choice in
            1)
                info "启动交互式环境配置..."
                bash "$SCRIPT_DIR/setup-env.sh"
                ;;
            2)
                if [ -f "$env_vault" ]; then
                    info "从保险库解密环境变量..."
                    node "$SCRIPT_DIR/env-vault.js" decrypt "$env_vault" "$env_file"
                else
                    error "未找到加密保险库文件: $env_vault"
                fi
                ;;
            3)
                info "请手动创建 .env 文件后重新运行部署脚本"
                exit 0
                ;;
            *)
                error "无效选择"
                ;;
        esac
    fi
    
    # 验证环境变量
    if [ -f "$env_file" ]; then
        info "验证环境变量配置..."
        if ! node "$SCRIPT_DIR/env-vault.js" validate "$env_file"; then
            error "环境变量验证失败，请检查配置"
        fi
    fi
    
    log "✅ 环境变量配置检查完成"
}

# 确认部署信息
confirm_deployment() {
    echo ""
    echo -e "${YELLOW}📋 部署信息确认:${NC}"
    echo "  项目: 公职猫微信转发服务"
    echo "  环境: $ENVIRONMENT"
    echo "  源码: $PROJECT_DIR"
    echo "  时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    
    # 显示环境配置
    if [ -f "$SCRIPT_DIR/deploy-config.json" ]; then
        local server_host=$(node -e "
            const config = require('$SCRIPT_DIR/deploy-config.json');
            const env = config.environments['$ENVIRONMENT'];
            console.log(env ? env.server.host : 'unknown');
        ")
        
        if [ "$server_host" != "unknown" ]; then
            echo "  目标服务器: $server_host"
            echo ""
        fi
    fi
    
    read -p "确认开始部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info "部署已取消"
        exit 0
    fi
}

# 执行部署
execute_deployment() {
    log "🚀 开始执行部署..."
    
    # 使用Node.js部署脚本
    if [ -f "$SCRIPT_DIR/deploy.js" ]; then
        info "使用高级部署脚本..."
        node "$SCRIPT_DIR/deploy.js" deploy "$ENVIRONMENT"
    else
        # 使用Shell部署脚本
        info "使用基础部署脚本..."
        if [ -f "$SCRIPT_DIR/auto-deploy.sh" ]; then
            bash "$SCRIPT_DIR/auto-deploy.sh"
        else
            error "未找到部署脚本文件"
        fi
    fi
}

# 部署后验证
post_deployment_verification() {
    log "🔍 执行部署后验证..."
    
    # 健康检查
    if [ -f "$SCRIPT_DIR/deploy.js" ]; then
        info "执行健康检查..."
        node "$SCRIPT_DIR/deploy.js" health "$ENVIRONMENT"
    fi
    
    # 显示服务状态
    info "检查服务状态..."
    if [ -f "$SCRIPT_DIR/deploy.js" ]; then
        node "$SCRIPT_DIR/deploy.js" status "$ENVIRONMENT"
    fi
    
    log "✅ 部署后验证完成"
}

# 显示部署结果
show_deployment_result() {
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    success "🎉 部署完成！"
    echo ""
    echo -e "${BLUE}📊 部署统计:${NC}"
    echo "  环境: $ENVIRONMENT"
    echo "  耗时: ${duration}秒"
    echo "  状态: 成功"
    echo ""
    
    echo -e "${YELLOW}📝 后续步骤:${NC}"
    echo "1. 在企业微信管理后台配置Webhook URL"
    echo "2. 测试微信消息转发功能"
    echo "3. 监控服务运行状态"
    echo ""
    
    echo -e "${BLUE}🔧 常用管理命令:${NC}"
    echo "  查看状态: node $SCRIPT_DIR/deploy.js status $ENVIRONMENT"
    echo "  健康检查: node $SCRIPT_DIR/deploy.js health $ENVIRONMENT"
    echo "  服务回滚: node $SCRIPT_DIR/deploy.js rollback $ENVIRONMENT"
    echo ""
    
    echo -e "${GREEN}✨ 部署成功完成！微信转发服务已就绪。${NC}"
}

# 验证服务架构
verify_service_architecture() {
    log "🏗️  验证重构后的服务架构..."

    # 检查重构后的服务文件
    local service_files=(
        "$PROJECT_DIR/service/service.js"
        "$PROJECT_DIR/service/WebhookService.js"
        "$PROJECT_DIR/service/MessageProcessService.js"
        "$PROJECT_DIR/service/UserBindingService.js"
        "$PROJECT_DIR/service/MessageSyncService.js"
        "$PROJECT_DIR/service/MediaDownloadService.js"
        "$PROJECT_DIR/service/pushService.js"
    )

    info "检查服务文件..."
    for file in "${service_files[@]}"; do
        if [ -f "$file" ]; then
            success "✅ $(basename "$file")"
        else
            error "❌ 服务文件不存在: $(basename "$file")"
            exit 1
        fi
    done

    # 运行服务架构验证测试
    if [ -f "$PROJECT_DIR/scripts/test-refactored-services.js" ]; then
        info "执行服务架构验证测试..."
        if node "$PROJECT_DIR/scripts/test-refactored-services.js"; then
            success "✅ 服务架构验证通过"
        else
            error "❌ 服务架构验证失败"
            exit 1
        fi
    fi

    # 运行数据库一致性验证
    if [ -f "$PROJECT_DIR/scripts/test-database-consistency.js" ]; then
        info "执行数据库一致性验证..."
        if node "$PROJECT_DIR/scripts/test-database-consistency.js"; then
            success "✅ 数据库一致性验证通过"
        else
            error "❌ 数据库一致性验证失败"
            exit 1
        fi
    fi

    success "🎉 服务架构验证完成"
}

# 错误处理
handle_error() {
    local exit_code=$?
    echo ""
    error "部署过程中发生错误 (退出码: $exit_code)"
    echo ""
    echo -e "${YELLOW}🔧 故障排除建议:${NC}"
    echo "1. 检查网络连接和服务器访问权限"
    echo "2. 验证环境变量配置是否正确"
    echo "3. 查看详细错误日志"
    echo "4. 如需回滚，运行: node $SCRIPT_DIR/deploy.js rollback $ENVIRONMENT"
    echo ""
    exit $exit_code
}

# 主函数
main() {
    # 设置错误处理
    trap 'handle_error' ERR
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 显示横幅
    show_banner
    
    # 执行部署流程
    check_dependencies
    select_environment
    check_env_config
    confirm_deployment
    execute_deployment
    verify_service_architecture
    post_deployment_verification
    show_deployment_result
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
