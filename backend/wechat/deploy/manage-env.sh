#!/bin/bash

# 微信转发服务环境变量管理脚本
# 用于确保关键配置不会被部署覆盖

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
SERVER_HOST="wechat.api.gongzhimall.com"
SERVER_USER="root"
DEPLOY_PATH="/www/wwwroot/wechat.api.gongzhimall.com"
ENV_FILE="$DEPLOY_PATH/.env"
BACKUP_DIR="/www/backup/wechat-service/env-backups"

# 关键环境变量列表
CRITICAL_ENV_VARS=(
    "WECHAT_CORP_ID"
    "WECHAT_CORP_SECRET" 
    "WECHAT_AGENT_ID"
    "WECHAT_TOKEN"
    "WECHAT_ENCODING_AES_KEY"
    "WECHAT_DEFAULT_OPEN_KFID"
    "WECHAT_BINDING_SECRET"
    "MYSQL_HOST"
    "MYSQL_PORT"
    "MYSQL_USER"
    "MYSQL_PASSWORD"
    "MYSQL_DATABASE"
)

# 打印帮助信息
print_help() {
    echo -e "${BLUE}微信转发服务环境变量管理工具${NC}"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  backup      备份当前环境变量"
    echo "  restore     从备份恢复环境变量"
    echo "  check       检查关键环境变量"
    echo "  update      更新特定环境变量"
    echo "  list        列出所有环境变量"
    echo "  help        显示此帮助信息"
    echo ""
}

# 备份环境变量
backup_env() {
    echo -e "${YELLOW}正在备份环境变量...${NC}"
    
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="env_backup_$timestamp.env"
    
    ssh $SERVER_USER@$SERVER_HOST "
        mkdir -p $BACKUP_DIR
        cp $ENV_FILE $BACKUP_DIR/$backup_file
        echo '备份已保存到: $BACKUP_DIR/$backup_file'
    "
    
    echo -e "${GREEN}✅ 环境变量备份完成${NC}"
}

# 检查关键环境变量
check_env() {
    echo -e "${YELLOW}正在检查关键环境变量...${NC}"
    
    local missing_vars=()
    
    for var in "${CRITICAL_ENV_VARS[@]}"; do
        local value=$(ssh $SERVER_USER@$SERVER_HOST "cd $DEPLOY_PATH && grep '^$var=' .env | cut -d'=' -f2- || echo ''")
        
        if [ -z "$value" ]; then
            missing_vars+=("$var")
            echo -e "${RED}❌ $var: 未设置${NC}"
        else
            # 隐藏敏感信息
            if [[ "$var" == *"SECRET"* ]] || [[ "$var" == *"PASSWORD"* ]] || [[ "$var" == *"TOKEN"* ]]; then
                echo -e "${GREEN}✅ $var: ****${NC}"
            else
                echo -e "${GREEN}✅ $var: $value${NC}"
            fi
        fi
    done
    
    if [ ${#missing_vars[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ 所有关键环境变量都已正确设置${NC}"
    else
        echo -e "${RED}❌ 缺少以下关键环境变量: ${missing_vars[*]}${NC}"
        return 1
    fi
}

# 更新环境变量
update_env() {
    if [ $# -lt 2 ]; then
        echo -e "${RED}错误: 请提供变量名和值${NC}"
        echo "用法: $0 update VARIABLE_NAME VARIABLE_VALUE"
        return 1
    fi
    
    local var_name="$1"
    local var_value="$2"
    
    echo -e "${YELLOW}正在更新环境变量 $var_name...${NC}"
    
    # 先备份
    backup_env
    
    # 更新变量
    ssh $SERVER_USER@$SERVER_HOST "
        cd $DEPLOY_PATH
        
        # 删除旧的变量（如果存在）
        sed -i '/^$var_name=/d' .env
        
        # 添加新的变量
        echo '$var_name=$var_value' >> .env
        
        echo '环境变量已更新'
    "
    
    echo -e "${GREEN}✅ 环境变量 $var_name 更新完成${NC}"
    echo -e "${YELLOW}请重启服务以使更改生效: pm2 restart gongzhimall-wechat --update-env${NC}"
}

# 列出所有环境变量
list_env() {
    echo -e "${YELLOW}当前环境变量:${NC}"
    
    ssh $SERVER_USER@$SERVER_HOST "
        cd $DEPLOY_PATH
        echo '===================='
        cat .env | while IFS='=' read -r key value; do
            if [[ \$key == *'SECRET'* ]] || [[ \$key == *'PASSWORD'* ]] || [[ \$key == *'TOKEN'* ]]; then
                echo \"\$key=****\"
            else
                echo \"\$key=\$value\"
            fi
        done
        echo '===================='
    "
}

# 恢复环境变量
restore_env() {
    echo -e "${YELLOW}可用的备份文件:${NC}"
    
    ssh $SERVER_USER@$SERVER_HOST "ls -la $BACKUP_DIR/ | grep env_backup"
    
    echo ""
    read -p "请输入要恢复的备份文件名: " backup_file
    
    if [ -z "$backup_file" ]; then
        echo -e "${RED}错误: 未指定备份文件${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}正在恢复环境变量...${NC}"
    
    ssh $SERVER_USER@$SERVER_HOST "
        if [ -f $BACKUP_DIR/$backup_file ]; then
            cp $BACKUP_DIR/$backup_file $ENV_FILE
            echo '环境变量已从备份恢复'
        else
            echo '错误: 备份文件不存在'
            exit 1
        fi
    "
    
    echo -e "${GREEN}✅ 环境变量恢复完成${NC}"
    echo -e "${YELLOW}请重启服务以使更改生效: pm2 restart gongzhimall-wechat --update-env${NC}"
}

# 主函数
main() {
    case "${1:-help}" in
        backup)
            backup_env
            ;;
        check)
            check_env
            ;;
        update)
            shift
            update_env "$@"
            ;;
        list)
            list_env
            ;;
        restore)
            restore_env
            ;;
        help|*)
            print_help
            ;;
    esac
}

# 执行主函数
main "$@"
