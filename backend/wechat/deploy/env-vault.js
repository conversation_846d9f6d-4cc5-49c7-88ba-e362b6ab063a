#!/usr/bin/env node

/**
 * 公职猫微信转发服务 - 环境变量加密保险库
 * 用于安全地存储和传输敏感的环境变量
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

class EnvironmentVault {
    constructor() {
        this.algorithm = 'aes-256-gcm';
        this.keyLength = 32;
        this.ivLength = 16;
        this.tagLength = 16;
        this.saltLength = 32;
        
        this.scriptDir = __dirname;
        this.projectDir = path.dirname(this.scriptDir);
        this.vaultFile = path.join(this.scriptDir, '.env.vault');
        this.keyFile = path.join(this.scriptDir, '.vault.key');
    }

    // 生成密钥
    generateKey(password, salt) {
        return crypto.pbkdf2Sync(password, salt, 100000, this.keyLength, 'sha256');
    }

    // 加密数据
    encrypt(data, password) {
        const salt = crypto.randomBytes(this.saltLength);
        const key = this.generateKey(password, salt);
        const iv = crypto.randomBytes(this.ivLength);
        
        const cipher = crypto.createCipherGCM(this.algorithm, key, iv);
        
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        const tag = cipher.getAuthTag();
        
        // 组合所有数据
        const result = {
            salt: salt.toString('hex'),
            iv: iv.toString('hex'),
            tag: tag.toString('hex'),
            data: encrypted
        };
        
        return JSON.stringify(result);
    }

    // 解密数据
    decrypt(encryptedData, password) {
        const parsed = JSON.parse(encryptedData);
        
        const salt = Buffer.from(parsed.salt, 'hex');
        const iv = Buffer.from(parsed.iv, 'hex');
        const tag = Buffer.from(parsed.tag, 'hex');
        const encrypted = parsed.data;
        
        const key = this.generateKey(password, salt);
        
        const decipher = crypto.createDecipherGCM(this.algorithm, key, iv);
        decipher.setAuthTag(tag);
        
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
    }

    // 读取密码
    async readPassword(prompt = '请输入保险库密码: ') {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        return new Promise((resolve) => {
            rl.question(prompt, (password) => {
                rl.close();
                resolve(password);
            });
        });
    }

    // 加密环境变量文件
    async encryptEnvFile(envFilePath, outputPath = null) {
        try {
            // 检查文件是否存在
            if (!fs.existsSync(envFilePath)) {
                throw new Error(`环境变量文件不存在: ${envFilePath}`);
            }

            // 读取环境变量文件
            const envContent = fs.readFileSync(envFilePath, 'utf8');
            
            // 获取密码
            const password = await this.readPassword('请输入加密密码: ');
            if (!password) {
                throw new Error('密码不能为空');
            }

            // 加密内容
            console.log('🔐 正在加密环境变量...');
            const encrypted = this.encrypt(envContent, password);
            
            // 保存加密文件
            const output = outputPath || this.vaultFile;
            fs.writeFileSync(output, encrypted);
            
            // 设置文件权限
            fs.chmodSync(output, 0o600);
            
            console.log(`✅ 环境变量已加密保存到: ${output}`);
            console.log('⚠️  请妥善保管加密密码，丢失后无法恢复数据');
            
        } catch (error) {
            console.error(`❌ 加密失败: ${error.message}`);
            process.exit(1);
        }
    }

    // 解密环境变量文件
    async decryptEnvFile(vaultFilePath = null, outputPath = null) {
        try {
            const vaultPath = vaultFilePath || this.vaultFile;
            
            // 检查保险库文件是否存在
            if (!fs.existsSync(vaultPath)) {
                throw new Error(`保险库文件不存在: ${vaultPath}`);
            }

            // 读取加密文件
            const encryptedContent = fs.readFileSync(vaultPath, 'utf8');
            
            // 获取密码
            const password = await this.readPassword('请输入解密密码: ');
            if (!password) {
                throw new Error('密码不能为空');
            }

            // 解密内容
            console.log('🔓 正在解密环境变量...');
            const decrypted = this.decrypt(encryptedContent, password);
            
            if (outputPath) {
                // 保存到指定文件
                fs.writeFileSync(outputPath, decrypted);
                fs.chmodSync(outputPath, 0o600);
                console.log(`✅ 环境变量已解密保存到: ${outputPath}`);
            } else {
                // 输出到控制台
                console.log('📋 解密的环境变量内容:');
                console.log('----------------------------------------');
                console.log(decrypted);
                console.log('----------------------------------------');
            }
            
        } catch (error) {
            console.error(`❌ 解密失败: ${error.message}`);
            process.exit(1);
        }
    }

    // 远程部署环境变量
    async deployEnvToServer(environment = 'production', vaultFilePath = null) {
        try {
            const configFile = path.join(this.scriptDir, 'deploy-config.json');
            const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
            
            const env = config.environments[environment];
            if (!env) {
                throw new Error(`未找到环境配置: ${environment}`);
            }

            const vaultPath = vaultFilePath || this.vaultFile;
            
            // 检查保险库文件
            if (!fs.existsSync(vaultPath)) {
                throw new Error(`保险库文件不存在: ${vaultPath}`);
            }

            // 读取并解密环境变量
            const encryptedContent = fs.readFileSync(vaultPath, 'utf8');
            const password = await this.readPassword('请输入解密密码: ');
            const decrypted = this.decrypt(encryptedContent, password);
            
            // 创建临时文件
            const tempFile = path.join('/tmp', `env-${Date.now()}.tmp`);
            fs.writeFileSync(tempFile, decrypted);
            fs.chmodSync(tempFile, 0o600);
            
            console.log(`🚀 正在部署环境变量到 ${environment} 环境...`);
            
            // 上传到服务器
            const { execSync } = require('child_process');
            const scpCommand = `scp ${tempFile} ${env.server.user}@${env.server.host}:${env.server.deployPath}/.env`;
            execSync(scpCommand);
            
            // 设置服务器文件权限
            const sshCommand = `ssh ${env.server.user}@${env.server.host} "chmod 600 ${env.server.deployPath}/.env"`;
            execSync(sshCommand);
            
            // 删除临时文件
            fs.unlinkSync(tempFile);
            
            console.log('✅ 环境变量部署完成');
            console.log('⚠️  建议重启服务以应用新的环境变量');
            
        } catch (error) {
            console.error(`❌ 部署失败: ${error.message}`);
            process.exit(1);
        }
    }

    // 验证环境变量
    validateEnvFile(envFilePath) {
        try {
            const envContent = fs.readFileSync(envFilePath, 'utf8');
            const lines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
            
            const requiredVars = [
                'MYSQL_PASSWORD',
                'WECHAT_CORP_SECRET',
                'JPUSH_MASTER_SECRET',
                'JWT_SECRET',
                'FILE_ENCRYPTION_KEY',
                'API_SECRET_KEY'
            ];
            
            const foundVars = {};
            const issues = [];
            
            lines.forEach(line => {
                const [key, value] = line.split('=', 2);
                if (key && value) {
                    foundVars[key.trim()] = value.trim();
                }
            });
            
            // 检查必需变量
            requiredVars.forEach(varName => {
                if (!foundVars[varName]) {
                    issues.push(`缺少必需变量: ${varName}`);
                } else if (foundVars[varName].length < 8) {
                    issues.push(`变量 ${varName} 值过短，建议至少8个字符`);
                }
            });
            
            // 检查密钥长度
            const keyVars = ['JWT_SECRET', 'FILE_ENCRYPTION_KEY', 'API_SECRET_KEY'];
            keyVars.forEach(varName => {
                if (foundVars[varName] && foundVars[varName].length < 32) {
                    issues.push(`密钥 ${varName} 长度不足，建议至少32个字符`);
                }
            });
            
            if (issues.length === 0) {
                console.log('✅ 环境变量验证通过');
                return true;
            } else {
                console.log('❌ 环境变量验证失败:');
                issues.forEach(issue => console.log(`  - ${issue}`));
                return false;
            }
            
        } catch (error) {
            console.error(`❌ 验证失败: ${error.message}`);
            return false;
        }
    }

    // 生成示例环境变量
    generateSampleEnv() {
        const sampleEnv = `# 公职猫微信转发服务环境变量配置
# 这是一个示例文件，请根据实际情况修改

# ==================== 基础配置 ====================
NODE_ENV=production
PORT=3000
SERVER_DOMAIN=wechat.api.gongzhimall.com

# ==================== 数据库配置 ====================
MYSQL_HOST=**********
MYSQL_PORT=3306
MYSQL_USER=wechat_user
MYSQL_PASSWORD=your_secure_password_here
MYSQL_DATABASE=gongzhimall_wechat

# ==================== 企业微信配置 ====================
WECHAT_CORP_ID=your_corp_id_here
WECHAT_CORP_SECRET=your_corp_secret_here
WECHAT_AGENT_ID=1000002
WECHAT_TOKEN=your_token_here
WECHAT_ENCODING_AES_KEY=your_aes_key_here

# ==================== 极光推送配置 ====================
JPUSH_APP_KEY=your_jpush_app_key_here
JPUSH_MASTER_SECRET=your_jpush_master_secret_here

# ==================== 安全配置 ====================
JWT_SECRET=your_jwt_secret_at_least_32_characters_long
FILE_ENCRYPTION_KEY=your_file_encryption_key_32_chars
API_SECRET_KEY=your_api_secret_key_32_characters

# ==================== 文件存储配置 ====================
FILE_STORAGE_PATH=/var/www/cache
MAX_FILE_SIZE=104857600
FILE_CLEANUP_INTERVAL=3600000
FILE_MAX_AGE_DAYS=3

# ==================== 日志配置 ====================
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/wechat-service
ENABLE_DB_LOGGING=true
`;

        const samplePath = path.join(this.projectDir, '.env.example');
        fs.writeFileSync(samplePath, sampleEnv);
        console.log(`✅ 示例环境变量文件已生成: ${samplePath}`);
        console.log('📝 请根据实际情况修改配置值');
    }

    // 显示帮助信息
    showHelp() {
        console.log(`
公职猫微信转发服务 - 环境变量保险库工具

用法:
  node env-vault.js <command> [options]

命令:
  encrypt <env-file>              加密环境变量文件
  decrypt [vault-file] [output]   解密环境变量文件
  deploy <environment>            部署环境变量到服务器
  validate <env-file>             验证环境变量文件
  sample                          生成示例环境变量文件
  help                            显示帮助信息

示例:
  node env-vault.js encrypt .env
  node env-vault.js decrypt .env.vault .env
  node env-vault.js deploy production
  node env-vault.js validate .env
  node env-vault.js sample

注意:
  - 加密密码请妥善保管，丢失后无法恢复数据
  - 环境变量文件包含敏感信息，请勿提交到版本控制系统
  - 建议定期更新密钥以确保安全性
        `);
    }
}

// 命令行接口
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    const vault = new EnvironmentVault();
    
    switch (command) {
        case 'encrypt':
            if (!args[1]) {
                console.error('❌ 请指定要加密的环境变量文件');
                process.exit(1);
            }
            await vault.encryptEnvFile(args[1], args[2]);
            break;
            
        case 'decrypt':
            await vault.decryptEnvFile(args[1], args[2]);
            break;
            
        case 'deploy':
            await vault.deployEnvToServer(args[1] || 'production', args[2]);
            break;
            
        case 'validate':
            if (!args[1]) {
                console.error('❌ 请指定要验证的环境变量文件');
                process.exit(1);
            }
            vault.validateEnvFile(args[1]);
            break;
            
        case 'sample':
            vault.generateSampleEnv();
            break;
            
        case 'help':
        default:
            vault.showHelp();
            break;
    }
}

if (require.main === module) {
    main().catch(error => {
        console.error(`❌ 执行失败: ${error.message}`);
        process.exit(1);
    });
}

module.exports = EnvironmentVault;
