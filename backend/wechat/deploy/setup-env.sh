#!/bin/bash

# 公职猫微信转发服务 - 环境变量配置脚本
# 用于安全地配置生产环境变量

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置文件路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENV_TEMPLATE="$PROJECT_DIR/config/env.template"
ENV_FILE="$PROJECT_DIR/.env"
ENV_PRODUCTION="$PROJECT_DIR/.env.production"

log() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 检查必需文件
check_files() {
    log "🔍 检查配置文件..."
    
    if [ ! -f "$ENV_TEMPLATE" ]; then
        error "未找到环境变量模板文件: $ENV_TEMPLATE"
    fi
    
    log "✅ 配置文件检查通过"
}

# 生成安全的随机密钥
generate_secret() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# 交互式配置环境变量
interactive_setup() {
    log "⚙️  开始交互式环境配置..."
    
    echo ""
    echo -e "${BLUE}=== 公职猫微信转发服务环境配置 ===${NC}"
    echo ""
    
    # 服务器配置
    echo -e "${YELLOW}📡 服务器配置${NC}"
    read -p "服务器域名 [wechat.api.gongzhimall.com]: " SERVER_DOMAIN
    SERVER_DOMAIN=${SERVER_DOMAIN:-wechat.api.gongzhimall.com}
    
    read -p "服务端口 [3000]: " PORT
    PORT=${PORT:-3000}
    
    # 数据库配置
    echo ""
    echo -e "${YELLOW}🗄️  数据库配置${NC}"
    read -p "MySQL主机地址 [**********]: " MYSQL_HOST
    MYSQL_HOST=${MYSQL_HOST:-**********}
    
    read -p "MySQL端口 [3306]: " MYSQL_PORT
    MYSQL_PORT=${MYSQL_PORT:-3306}
    
    read -p "数据库名称 [gongzhimall_wechat]: " MYSQL_DATABASE
    MYSQL_DATABASE=${MYSQL_DATABASE:-gongzhimall_wechat}
    
    read -p "数据库用户名 [wechat_user]: " MYSQL_USER
    MYSQL_USER=${MYSQL_USER:-wechat_user}
    
    read -s -p "数据库密码: " MYSQL_PASSWORD
    echo ""
    
    if [ -z "$MYSQL_PASSWORD" ]; then
        error "数据库密码不能为空"
    fi
    
    # 企业微信配置
    echo ""
    echo -e "${YELLOW}💬 企业微信配置${NC}"
    read -p "企业ID [ww857dc7bfe97b085b]: " WECHAT_CORP_ID
    WECHAT_CORP_ID=${WECHAT_CORP_ID:-ww857dc7bfe97b085b}
    
    read -s -p "企业微信Secret: " WECHAT_CORP_SECRET
    echo ""
    
    if [ -z "$WECHAT_CORP_SECRET" ]; then
        error "企业微信Secret不能为空"
    fi
    
    read -p "应用ID [1000002]: " WECHAT_AGENT_ID
    WECHAT_AGENT_ID=${WECHAT_AGENT_ID:-1000002}
    
    read -p "Token [gongzhimallEnterprise2025]: " WECHAT_TOKEN
    WECHAT_TOKEN=${WECHAT_TOKEN:-gongzhimallEnterprise2025}
    
    read -p "EncodingAESKey [zlb6q9HgvrGOYlgMR9HNmMNIHo9dJgrQVmbs7G2DJcv]: " WECHAT_ENCODING_AES_KEY
    WECHAT_ENCODING_AES_KEY=${WECHAT_ENCODING_AES_KEY:-zlb6q9HgvrGOYlgMR9HNmMNIHo9dJgrQVmbs7G2DJcv}
    
    # 极光推送配置
    echo ""
    echo -e "${YELLOW}📱 极光推送配置${NC}"
    read -p "JPush App Key [bd2c958b83dc679759a25664]: " JPUSH_APP_KEY
    JPUSH_APP_KEY=${JPUSH_APP_KEY:-bd2c958b83dc679759a25664}
    
    read -s -p "JPush Master Secret: " JPUSH_MASTER_SECRET
    echo ""
    
    if [ -z "$JPUSH_MASTER_SECRET" ]; then
        error "JPush Master Secret不能为空"
    fi
    
    # 生成加密密钥
    echo ""
    echo -e "${YELLOW}🔐 安全配置${NC}"
    info "正在生成加密密钥..."
    
    JWT_SECRET=$(generate_secret 64)
    FILE_ENCRYPTION_KEY=$(generate_secret 32)
    API_SECRET_KEY=$(generate_secret 32)
    
    log "✅ 安全密钥生成完成"
}

# 生成环境变量文件
generate_env_file() {
    log "📝 生成环境变量文件..."
    
    cat > "$ENV_FILE" << EOF
# 公职猫微信转发服务环境变量配置
# 生成时间: $(date '+%Y-%m-%d %H:%M:%S')
# 环境: production

# ==================== 基础配置 ====================
NODE_ENV=production
PORT=$PORT
SERVER_DOMAIN=$SERVER_DOMAIN

# ==================== 数据库配置 ====================
MYSQL_HOST=$MYSQL_HOST
MYSQL_PORT=$MYSQL_PORT
MYSQL_USER=$MYSQL_USER
MYSQL_PASSWORD=$MYSQL_PASSWORD
MYSQL_DATABASE=$MYSQL_DATABASE

# 数据库连接池配置
MYSQL_CONNECTION_LIMIT=10
MYSQL_QUEUE_LIMIT=0
MYSQL_TIMEOUT=60000
MYSQL_ACQUIRE_TIMEOUT=60000

# ==================== 企业微信配置 ====================
WECHAT_CORP_ID=$WECHAT_CORP_ID
WECHAT_CORP_SECRET=$WECHAT_CORP_SECRET
WECHAT_AGENT_ID=$WECHAT_AGENT_ID
WECHAT_TOKEN=$WECHAT_TOKEN
WECHAT_ENCODING_AES_KEY=$WECHAT_ENCODING_AES_KEY

# ==================== 极光推送配置 ====================
JPUSH_APP_KEY=$JPUSH_APP_KEY
JPUSH_MASTER_SECRET=$JPUSH_MASTER_SECRET
JPUSH_PRODUCTION=true

# ==================== 安全配置 ====================
JWT_SECRET=$JWT_SECRET
FILE_ENCRYPTION_KEY=$FILE_ENCRYPTION_KEY
API_SECRET_KEY=$API_SECRET_KEY

# ==================== 文件存储配置 ====================
FILE_STORAGE_PATH=/var/www/cache
MAX_FILE_SIZE=104857600
FILE_CLEANUP_INTERVAL=3600000
FILE_MAX_AGE_DAYS=3

# ==================== 日志配置 ====================
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/wechat-service
LOG_FILE_MAX_SIZE=10485760
LOG_FILE_MAX_FILES=5
ENABLE_DB_LOGGING=true

# ==================== 监控配置 ====================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health
ENABLE_PERFORMANCE_MONITORING=true

# ==================== 调试配置 ====================
DEBUG=false
VERBOSE_LOGGING=false
EOF

    # 设置文件权限
    chmod 600 "$ENV_FILE"
    
    log "✅ 环境变量文件已生成: $ENV_FILE"
}

# 验证配置
validate_config() {
    log "🔍 验证配置..."
    
    # 检查必需的环境变量
    local required_vars=(
        "MYSQL_PASSWORD"
        "WECHAT_CORP_SECRET"
        "JPUSH_MASTER_SECRET"
        "JWT_SECRET"
        "FILE_ENCRYPTION_KEY"
        "API_SECRET_KEY"
    )
    
    source "$ENV_FILE"
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            error "必需的环境变量 $var 未设置"
        fi
    done
    
    # 检查密钥长度
    if [ ${#JWT_SECRET} -lt 32 ]; then
        error "JWT_SECRET 长度不足，至少需要32个字符"
    fi
    
    if [ ${#FILE_ENCRYPTION_KEY} -lt 32 ]; then
        error "FILE_ENCRYPTION_KEY 长度不足，至少需要32个字符"
    fi
    
    log "✅ 配置验证通过"
}

# 创建生产环境副本
create_production_copy() {
    log "📋 创建生产环境配置副本..."
    
    cp "$ENV_FILE" "$ENV_PRODUCTION"
    chmod 600 "$ENV_PRODUCTION"
    
    log "✅ 生产环境配置已保存: $ENV_PRODUCTION"
}

# 显示配置摘要
show_summary() {
    echo ""
    echo -e "${GREEN}🎉 环境配置完成！${NC}"
    echo ""
    echo -e "${BLUE}📋 配置摘要:${NC}"
    echo "  服务器域名: $SERVER_DOMAIN"
    echo "  服务端口: $PORT"
    echo "  数据库主机: $MYSQL_HOST:$MYSQL_PORT"
    echo "  数据库名称: $MYSQL_DATABASE"
    echo "  企业微信ID: $WECHAT_CORP_ID"
    echo "  应用ID: $WECHAT_AGENT_ID"
    echo ""
    echo -e "${YELLOW}📝 重要提醒:${NC}"
    echo "1. 环境变量文件已生成，请妥善保管"
    echo "2. 生产环境部署前请再次确认所有配置"
    echo "3. 定期更新密钥以确保安全性"
    echo "4. 不要将 .env 文件提交到版本控制系统"
    echo ""
    echo -e "${BLUE}📁 生成的文件:${NC}"
    echo "  开发环境: $ENV_FILE"
    echo "  生产环境: $ENV_PRODUCTION"
    echo ""
}

# 主函数
main() {
    echo ""
    echo -e "${GREEN}🔧 公职猫微信转发服务环境配置工具${NC}"
    echo ""
    
    check_files
    interactive_setup
    generate_env_file
    validate_config
    create_production_copy
    show_summary
    
    log "🎉 环境配置完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
