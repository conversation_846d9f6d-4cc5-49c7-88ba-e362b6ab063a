#!/bin/bash

# 公职猫微信转发服务 - 自动化部署脚本
# 适用于腾讯云轻量应用服务器 + 宝塔面板环境
# 作者: 公职猫开发团队
# 版本: 1.0.0

set -e  # 遇到错误立即退出

# ==================== 配置区域 ====================
# 服务器配置
SERVER_HOST="wechat.api.gongzhimall.com"
SERVER_USER="root"
SERVER_PORT="22"
DEPLOY_PATH="/www/wwwroot/wechat.api.gongzhimall.com"
SERVICE_NAME="gongzhimall-wechat"

# 项目配置
PROJECT_NAME="公职猫微信转发服务"
LOCAL_PROJECT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKUP_DIR="/www/backup/wechat-service"
LOG_FILE="/var/log/deploy-wechat-service.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ==================== 工具函数 ====================
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE" 2>/dev/null || true
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    echo "[ERROR] $1" >> "$LOG_FILE" 2>/dev/null || true
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "[WARNING] $1" >> "$LOG_FILE" 2>/dev/null || true
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    echo "[INFO] $1" >> "$LOG_FILE" 2>/dev/null || true
}

# 检查必需的工具
check_dependencies() {
    log "🔍 检查部署依赖..."
    
    local missing_tools=()
    
    # 检查本地工具
    command -v rsync >/dev/null 2>&1 || missing_tools+=("rsync")
    command -v ssh >/dev/null 2>&1 || missing_tools+=("ssh")
    command -v scp >/dev/null 2>&1 || missing_tools+=("scp")
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        error "缺少必需工具: ${missing_tools[*]}。请先安装这些工具。"
    fi
    
    # 检查项目文件
    if [ ! -f "$LOCAL_PROJECT_PATH/package.json" ]; then
        error "未找到 package.json 文件，请确认在正确的项目目录中运行此脚本"
    fi
    
    if [ ! -f "$LOCAL_PROJECT_PATH/index.js" ]; then
        error "未找到 index.js 文件，请确认项目结构完整"
    fi
    
    log "✅ 依赖检查通过"
}

# 检查服务器连接
check_server_connection() {
    log "🌐 检查服务器连接..."
    
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$SERVER_USER@$SERVER_HOST" "echo '连接成功'" >/dev/null 2>&1; then
        error "无法连接到服务器 $SERVER_HOST，请检查：
1. 服务器地址是否正确
2. SSH密钥是否已配置
3. 网络连接是否正常"
    fi
    
    log "✅ 服务器连接正常"
}

# 创建备份
create_backup() {
    log "💾 创建服务备份..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        # 创建备份目录
        mkdir -p $BACKUP_DIR
        
        # 如果服务目录存在，创建备份
        if [ -d '$DEPLOY_PATH' ]; then
            BACKUP_NAME=\"wechat-service-\$(date +%Y%m%d_%H%M%S).tar.gz\"
            cd '$DEPLOY_PATH/..'
            tar -czf '$BACKUP_DIR/\$BACKUP_NAME' \$(basename '$DEPLOY_PATH') 2>/dev/null || true
            echo \"备份已创建: $BACKUP_DIR/\$BACKUP_NAME\"
            
            # 保留最近5个备份
            cd '$BACKUP_DIR'
            ls -t wechat-service-*.tar.gz 2>/dev/null | tail -n +6 | xargs rm -f 2>/dev/null || true
        fi
    "
    
    log "✅ 备份创建完成"
}

# 停止服务
stop_service() {
    log "⏹️  停止现有服务..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        # 尝试通过PM2停止
        if command -v pm2 >/dev/null 2>&1; then
            pm2 stop '$SERVICE_NAME' 2>/dev/null || true
            pm2 delete '$SERVICE_NAME' 2>/dev/null || true
        fi
        
        # 尝试通过Docker停止
        if command -v docker >/dev/null 2>&1; then
            docker stop '$SERVICE_NAME' 2>/dev/null || true
            docker rm '$SERVICE_NAME' 2>/dev/null || true
        fi
        
        # 杀死可能残留的Node.js进程
        pkill -f 'node.*index.js' 2>/dev/null || true
        
        echo '服务已停止'
    "
    
    log "✅ 服务停止完成"
}

# 同步代码
sync_code() {
    log "📦 同步项目代码..."
    
    # 创建部署目录
    ssh "$SERVER_USER@$SERVER_HOST" "mkdir -p $DEPLOY_PATH"
    
    # 排除不需要的文件
    local exclude_patterns=(
        "node_modules/"
        ".git/"
        ".env"
        "*.log"
        "cache/"
        "logs/"
        "*.tmp"
        ".DS_Store"
        "deploy/"
    )
    
    local rsync_excludes=""
    for pattern in "${exclude_patterns[@]}"; do
        rsync_excludes="$rsync_excludes --exclude=$pattern"
    done
    
    # 同步代码
    rsync -avz --delete $rsync_excludes \
        "$LOCAL_PROJECT_PATH/" \
        "$SERVER_USER@$SERVER_HOST:$DEPLOY_PATH/"
    
    log "✅ 代码同步完成"
}

# 安装依赖
install_dependencies() {
    log "📚 安装项目依赖..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        cd '$DEPLOY_PATH'
        
        # 检查Node.js版本
        if ! command -v node >/dev/null 2>&1; then
            echo '错误: 未安装Node.js，请先在宝塔面板中安装Node.js 18+'
            exit 1
        fi
        
        NODE_VERSION=\$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
        if [ \"\$NODE_VERSION\" -lt 18 ]; then
            echo '错误: Node.js版本过低，需要18+版本'
            exit 1
        fi
        
        # 安装依赖
        if command -v yarn >/dev/null 2>&1; then
            yarn install --production --frozen-lockfile
        else
            npm install --production --no-package-lock
        fi
        
        echo '依赖安装完成'
    "
    
    log "✅ 依赖安装完成"
}

# 配置环境变量
setup_environment() {
    log "⚙️  配置环境变量..."
    
    # 检查本地环境变量模板
    if [ ! -f "$LOCAL_PROJECT_PATH/config/env.template" ]; then
        warning "未找到环境变量模板文件，跳过环境变量配置"
        return
    fi
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        cd '$DEPLOY_PATH'
        
        # 如果.env文件不存在，从模板创建
        if [ ! -f '.env' ]; then
            if [ -f 'config/env.template' ]; then
                cp config/env.template .env
                echo '已从模板创建.env文件，请手动配置环境变量'
            else
                echo '警告: 未找到环境变量配置文件'
            fi
        fi
        
        # 设置文件权限
        chmod 600 .env 2>/dev/null || true
        
        echo '环境变量配置完成'
    "
    
    log "✅ 环境变量配置完成"
}

# 启动服务
start_service() {
    log "🚀 启动服务..."
    
    ssh "$SERVER_USER@$SERVER_HOST" "
        cd '$DEPLOY_PATH'
        
        # 语法检查
        node -c index.js || {
            echo '错误: 代码语法检查失败'
            exit 1
        }
        
        # 尝试使用PM2启动
        if command -v pm2 >/dev/null 2>&1; then
            pm2 start index.js --name '$SERVICE_NAME' --env production
            pm2 save
            echo '服务已通过PM2启动'
        else
            # 后台启动
            nohup node index.js > logs/service.log 2>&1 &
            echo \$! > service.pid
            echo '服务已后台启动'
        fi
    "
    
    log "✅ 服务启动完成"
}

# 健康检查
health_check() {
    log "🏥 执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        info "健康检查尝试 $attempt/$max_attempts..."
        
        if curl -f -s "https://$SERVER_HOST/health" >/dev/null 2>&1; then
            log "✅ 服务健康检查通过"
            return 0
        fi
        
        sleep 2
        ((attempt++))
    done
    
    error "健康检查失败，服务可能未正常启动"
}

# 显示部署信息
show_deployment_info() {
    log "📋 部署信息汇总"
    echo ""
    echo -e "${GREEN}🎉 ${PROJECT_NAME} 部署成功！${NC}"
    echo ""
    echo -e "${BLUE}📡 服务地址:${NC} https://$SERVER_HOST"
    echo -e "${BLUE}🏥 健康检查:${NC} https://$SERVER_HOST/health"
    echo -e "${BLUE}🔗 Webhook地址:${NC} https://$SERVER_HOST/api/wechat/webhook"
    echo ""
    echo -e "${YELLOW}📝 后续步骤:${NC}"
    echo "1. 在企业微信管理后台配置Webhook URL"
    echo "2. 测试微信消息转发功能"
    echo "3. 检查服务日志确认运行状态"
    echo ""
    echo -e "${BLUE}🔧 管理命令:${NC}"
    echo "  查看服务状态: ssh $SERVER_USER@$SERVER_HOST 'pm2 status'"
    echo "  查看服务日志: ssh $SERVER_USER@$SERVER_HOST 'pm2 logs $SERVICE_NAME'"
    echo "  重启服务: ssh $SERVER_USER@$SERVER_HOST 'pm2 restart $SERVICE_NAME'"
    echo ""
}

# 主部署流程
main() {
    echo ""
    echo -e "${GREEN}🚀 开始部署 ${PROJECT_NAME}${NC}"
    echo -e "${BLUE}目标服务器:${NC} $SERVER_HOST"
    echo -e "${BLUE}部署路径:${NC} $DEPLOY_PATH"
    echo ""
    
    # 确认部署
    read -p "确认开始部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "部署已取消"
        exit 0
    fi
    
    # 执行部署步骤
    check_dependencies
    check_server_connection
    create_backup
    stop_service
    sync_code
    install_dependencies
    setup_environment
    start_service
    health_check
    show_deployment_info
    
    log "🎉 部署完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
