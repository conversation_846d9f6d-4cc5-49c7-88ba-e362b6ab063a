# 公职猫微信转发服务 - 自动化部署系统

## 📋 概述

本自动化部署系统为公职猫微信转发服务提供完整的一键部署解决方案，支持多环境部署、安全的环境变量管理、健康检查监控和自动回滚功能。

## 🚀 快速开始

### 一键部署

```bash
# 进入部署目录
cd backend/wechat/deploy

# 执行一键部署
bash one-click-deploy.sh
```

### 分步部署

```bash
# 1. 配置环境变量
bash setup-env.sh

# 2. 执行部署
node deploy.js deploy production

# 3. 检查状态
node deploy.js status production
```

## 📁 文件结构

```
deploy/
├── one-click-deploy.sh      # 一键部署主脚本
├── auto-deploy.sh           # 基础部署脚本
├── deploy.js                # 高级部署管理工具
├── setup-env.sh             # 环境变量配置脚本
├── env-vault.js             # 环境变量加密工具
├── deploy-config.json       # 部署配置文件
└── README.md                # 使用说明文档
```

## ⚙️ 配置说明

### 部署配置 (deploy-config.json)

配置文件包含以下主要部分：

- **environments**: 多环境配置（生产、测试等）
- **deployment**: 部署策略和参数
- **monitoring**: 监控和健康检查配置
- **security**: 安全配置
- **backup**: 备份策略

### 环境变量配置

支持多种配置方式：

1. **交互式配置**: 运行 `bash setup-env.sh`
2. **加密保险库**: 使用 `node env-vault.js`
3. **手动配置**: 直接编辑 `.env` 文件

## 🔧 部署工具详解

### 1. 一键部署脚本 (one-click-deploy.sh)

**功能特性:**
- 自动检查依赖和环境
- 交互式环境选择
- 集成环境变量配置
- 完整的部署流程
- 部署后验证

**使用方法:**
```bash
bash one-click-deploy.sh
```

### 2. 高级部署工具 (deploy.js)

**功能特性:**
- 多环境支持
- 健康检查
- 自动回滚
- 状态监控
- 备份管理

**使用方法:**
```bash
# 部署到生产环境
node deploy.js deploy production

# 部署到测试环境
node deploy.js deploy staging

# 检查服务状态
node deploy.js status production

# 执行健康检查
node deploy.js health production

# 回滚到上一版本
node deploy.js rollback production
```

### 3. 环境变量管理 (env-vault.js)

**功能特性:**
- 环境变量加密存储
- 安全传输
- 配置验证
- 远程部署

**使用方法:**
```bash
# 加密环境变量
node env-vault.js encrypt .env

# 解密环境变量
node env-vault.js decrypt .env.vault .env

# 部署到服务器
node env-vault.js deploy production

# 验证配置
node env-vault.js validate .env

# 生成示例配置
node env-vault.js sample
```

## 🌍 多环境支持

### 生产环境 (production)
- 域名: `wechat.api.gongzhimall.com`
- 端口: `3000`
- SSL: 启用
- 进程管理: PM2

### 测试环境 (staging)
- 域名: `staging.wechat.api.gongzhimall.com`
- 端口: `3001`
- SSL: 可选
- 进程管理: PM2

### 自定义环境
可以通过修改 `deploy-config.json` 添加新的环境配置。

## 🔐 安全特性

### 环境变量加密
- 使用 AES-256-GCM 加密算法
- PBKDF2 密钥派生
- 安全的密码保护

### 传输安全
- SSH 密钥认证
- 加密文件传输
- 临时文件自动清理

### 访问控制
- 文件权限管理
- 防火墙配置
- 安全头设置

## 📊 监控和健康检查

### 健康检查
- 自动服务状态检测
- 响应时间监控
- 错误率统计

### 日志管理
- 结构化日志记录
- 日志轮转
- 错误追踪

### 告警机制
- 服务异常告警
- 性能阈值监控
- 自动恢复机制

## 🔄 备份和回滚

### 自动备份
- 部署前自动备份
- 保留最近5个版本
- 压缩存储

### 快速回滚
- 一键回滚到上一版本
- 自动服务重启
- 健康检查验证

## 🚨 故障排除

### 常见问题

**1. SSH连接失败**
```bash
# 检查SSH密钥配置
ssh-add -l

# 测试连接
ssh user@server "echo 'test'"
```

**2. Node.js版本不兼容**
```bash
# 检查版本
node -v

# 升级Node.js (使用nvm)
nvm install 18
nvm use 18
```

**3. 环境变量配置错误**
```bash
# 验证配置
node env-vault.js validate .env

# 重新配置
bash setup-env.sh
```

**4. 服务启动失败**
```bash
# 检查日志
node deploy.js status production

# 查看详细错误
ssh user@server "pm2 logs gongzhimall-wechat"
```

### 紧急回滚

如果部署后发现问题，可以快速回滚：

```bash
# 立即回滚
node deploy.js rollback production

# 检查回滚状态
node deploy.js health production
```

## 📝 最佳实践

### 部署前检查
1. 确认代码已提交并推送
2. 验证环境变量配置
3. 检查服务器资源状况
4. 备份重要数据

### 部署流程
1. 选择合适的部署时间
2. 通知相关人员
3. 执行部署
4. 验证功能正常
5. 监控服务状态

### 安全建议
1. 定期更新密钥
2. 使用强密码
3. 限制服务器访问
4. 监控异常活动

## 🔗 相关链接

- [项目主文档](../README.md)
- [API接口文档](../docs/API_DOCUMENTATION.md)
- [宝塔面板部署指南](../docs/BAOTA_DEPLOYMENT_GUIDE.md)
- [部署检查清单](../DEPLOYMENT_CHECKLIST.md)

## 📞 技术支持

如遇到部署问题，请：

1. 查看本文档的故障排除部分
2. 检查服务器日志
3. 联系技术团队

---

**注意**: 请妥善保管部署密钥和环境变量，不要将敏感信息提交到版本控制系统。
