# 公职猫移动端环境变量配置模板
# 复制此文件为 .env 并填入真实的配置值

# ========================================
# 极光推送配置
# ========================================
# 极光推送AppKey - 从极光推送控制台获取
# 获取地址：https://www.jiguang.cn/
JPUSH_APP_KEY=your_jpush_app_key_here

# 推送渠道标识
JPUSH_CHANNEL=gongzhimall-official

# ========================================
# 微信相关配置
# ========================================
# 企业微信配置
WECHAT_CORP_ID=your_corp_id_here
WECHAT_AGENT_ID=your_agent_id_here
WECHAT_CORP_SECRET=your_corp_secret_here

# 微信开放平台配置
WECHAT_APP_ID=your_wechat_app_id_here
WECHAT_APP_SECRET=your_wechat_app_secret_here

# ========================================
# API服务配置
# ========================================
# 微信转发API基础地址
WECHAT_BINDING_API_BASE_URL=https://wechat.api.gongzhimall.com

# API认证配置
WECHAT_BINDING_TOKEN_SECRET=your_token_secret_here
WECHAT_BINDING_MOBILE_APP_TOKEN=your_mobile_app_token_here

# ========================================
# 其他配置
# ========================================

# 同步配置
WECHAT_SYNC_BATCH_SIZE=50
WECHAT_SYNC_MAX_CONCURRENT_MEDIA=3
WECHAT_SYNC_TIMEOUT=60000

# ========================================
# 开发环境配置
# ========================================
NODE_ENV=development

# ========================================
# 重要说明
# ========================================
# 1. 此文件为开发环境配置模板，请使用测试配置
# 2. 生产环境配置通过服务器环境变量设置，不在代码中存储
# 3. 请勿在任何配置文件中填写真实的生产环境敏感信息
# 4. 复制此文件为 .env 并根据开发需要修改配置值
# 5. .env 文件已加入 .gitignore，不会被提交到版本控制
# 6. 使用 yarn wechat:validate 命令验证配置是否正确