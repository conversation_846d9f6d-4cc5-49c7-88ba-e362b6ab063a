/**
 * 微信绑定配置管理模块
 * 提供生产级别的配置管理，避免硬编码
 */

import { weChatConfig } from './appConfig';

// 环境变量接口
interface EnvironmentConfig {
  API_BASE_URL: string;
  TOKEN_SECRET: string;
  MOBILE_APP_TOKEN: string;
  CORP_ID: string;
  CUSTOMER_SERVICE_URL: string;
  APP_VERSION: string;
  PLATFORM: 'android' | 'ios';
}

// 绑定配置接口
interface BindingConfig {
  api: {
    baseUrl: string;
    endpoints: {
      registerDevice: string;
      status: string;
      oneClickBinding: string;
      unbind: string;
    };
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
  };
  auth: {
    tokenSecret: string;
    mobileAppToken: string;
    signatureTimeout: number;
  };
  wechat: {
    corpId: string;
    customerServiceUrl: string;
    openPlatform: {
      appId: string;
      universalLink: string;
    };
  };
  polling: {
    interval: number;
    maxAttempts: number;
    timeout: number;
  };
  storage: {
    keys: {
      bindingToken: string;
      bindingTime: string;
      userUuid: string;
      deviceId: string;
    };
  };
  app: {
    version: string;
    platform: 'android' | 'ios';
  };
}

// 导入统一的设备管理器
import { userDeviceManager } from '../utils/UserDeviceManager';

/**
 * 获取当前平台（使用统一管理器）
 */
function getCurrentPlatform(): 'android' | 'ios' {
  return userDeviceManager.getCurrentPlatform();
}

/**
 * 获取应用版本（使用统一管理器）
 */
function getAppVersion(): string {
  return userDeviceManager.getAppVersion();
}

/**
 * 从环境变量获取配置
 */
function getEnvironmentConfig(): EnvironmentConfig {
  return {
    API_BASE_URL: process.env.WECHAT_BINDING_API_BASE_URL || weChatConfig.API_BASE_URL,
    TOKEN_SECRET: process.env.WECHAT_BINDING_TOKEN_SECRET || 'gongzhimall-app-2025',
    MOBILE_APP_TOKEN: process.env.WECHAT_BINDING_MOBILE_APP_TOKEN || 'mobile-app-token',
    CORP_ID: process.env.WECHAT_CORP_ID || weChatConfig.WECHAT_CORP_ID,
    CUSTOMER_SERVICE_URL: process.env.WECHAT_CUSTOMER_SERVICE_URL || weChatConfig.WECHAT_CUSTOMER_SERVICE_URL,
    APP_VERSION: getAppVersion(),
    PLATFORM: getCurrentPlatform(),
  };
}

/**
 * 创建绑定配置
 */
function createBindingConfig(): BindingConfig {
  const env = getEnvironmentConfig();

  return {
    api: {
      baseUrl: env.API_BASE_URL,
      endpoints: {
        registerDevice: '/api/sync/register-device',
        status: '/api/bind/status',
        oneClickBinding: '/api/bind/one-click-link',
        unbind: '/api/bind/unbind',
      },
      timeout: parseInt(process.env.WECHAT_BINDING_API_TIMEOUT || '30000', 10),
      retryAttempts: parseInt(process.env.WECHAT_BINDING_RETRY_ATTEMPTS || '3', 10),
      retryDelay: parseInt(process.env.WECHAT_BINDING_RETRY_DELAY || '5000', 10),
    },
    auth: {
      tokenSecret: env.TOKEN_SECRET,
      mobileAppToken: env.MOBILE_APP_TOKEN,
      signatureTimeout: parseInt(process.env.WECHAT_BINDING_SIGNATURE_TIMEOUT || '300000', 10), // 5分钟
    },
    wechat: {
      corpId: env.CORP_ID,
      customerServiceUrl: env.CUSTOMER_SERVICE_URL,
      openPlatform: {
        appId: weChatConfig.WECHAT_OPEN_PLATFORM.APP_ID,
        universalLink: weChatConfig.WECHAT_OPEN_PLATFORM.UNIVERSAL_LINK,
      },
    },
    // 注意：新架构采用纯推送驱动，但绑定过程仍需要轮询确认绑定状态
    polling: {
      interval: parseInt(process.env.WECHAT_BINDING_POLL_INTERVAL || '10000', 10), // 10秒
      maxAttempts: parseInt(process.env.WECHAT_BINDING_POLL_MAX_ATTEMPTS || '60', 10), // 60次
      timeout: parseInt(process.env.WECHAT_BINDING_POLL_TIMEOUT || '600000', 10), // 10分钟
    },
    storage: {
      keys: {
        bindingToken: 'wechat_binding_token',
        bindingTime: 'wechat_binding_time',
        userUuid: 'user_uuid',
        deviceId: 'app_device_id',
      },
    },
    app: {
      version: env.APP_VERSION,
      platform: env.PLATFORM,
    },
  };
}

/**
 * 验证配置完整性
 */
function validateConfig(config: BindingConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 验证API配置
  if (!config.api.baseUrl || config.api.baseUrl.includes('xxx')) {
    errors.push('API基础URL未正确配置');
  }

  if (!config.api.baseUrl.startsWith('https://')) {
    errors.push('API基础URL必须使用HTTPS');
  }

  // 验证认证配置
  if (!config.auth.tokenSecret || config.auth.tokenSecret.length < 16) {
    errors.push('Token密钥未配置或长度不足');
  }

  if (!config.auth.mobileAppToken) {
    errors.push('移动应用Token未配置');
  }

  // 验证微信配置
  if (!config.wechat.corpId || config.wechat.corpId.includes('your-corp-id')) {
    errors.push('企业微信CorpID未正确配置');
  }

  if (!config.wechat.customerServiceUrl) {
    errors.push('企业微信客服URL未配置');
  }

  if (!config.wechat.openPlatform.appId || config.wechat.openPlatform.appId.includes('XXX')) {
    errors.push('微信开放平台AppID未正确配置');
  }

  // 验证轮询配置
  if (config.polling.interval < 5000) {
    errors.push('轮询间隔不能小于5秒');
  }

  if (config.polling.maxAttempts < 10) {
    errors.push('轮询最大次数不能小于10次');
  }

  // 验证超时配置
  if (config.api.timeout < 10000) {
    errors.push('API超时时间不能小于10秒');
  }

  if (config.auth.signatureTimeout < 60000) {
    errors.push('签名超时时间不能小于1分钟');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * 微信绑定配置管理器
 */
export class WeChatBindingConfigManager {
  private static instance: WeChatBindingConfigManager;
  private config: BindingConfig;
  private validated: boolean = false;

  private constructor() {
    this.config = createBindingConfig();
  }

  public static getInstance(): WeChatBindingConfigManager {
    if (!WeChatBindingConfigManager.instance) {
      WeChatBindingConfigManager.instance = new WeChatBindingConfigManager();
    }
    return WeChatBindingConfigManager.instance;
  }

  /**
   * 获取配置
   */
  public getConfig(): BindingConfig {
    if (!this.validated) {
      this.validateAndLog();
    }
    return this.config;
  }

  /**
   * 验证并记录配置
   */
  private validateAndLog(): void {
    const validation = validateConfig(this.config);

    if (!validation.valid) {
      console.error('[WeChatBindingConfig] 配置验证失败:', validation.errors);

      // 提供配置修复建议
      const suggestions = this.generateConfigSuggestions(validation.errors);
      console.error('[WeChatBindingConfig] 配置修复建议:', suggestions);

      // 在生产环境下也抛出错误，确保配置正确
      throw new Error(`微信绑定配置验证失败: ${validation.errors.join(', ')}\n修复建议: ${suggestions.join(', ')}`);
    } else {
      console.log('[WeChatBindingConfig] 配置验证通过');
    }

    this.validated = true;
  }

  /**
   * 生成配置修复建议
   */
  private generateConfigSuggestions(errors: string[]): string[] {
    const suggestions: string[] = [];

    errors.forEach(error => {
      if (error.includes('API基础URL')) {
        suggestions.push('请设置环境变量 WECHAT_BINDING_API_BASE_URL 或检查 weChatConfig.API_BASE_URL');
      }
      if (error.includes('Token密钥')) {
        suggestions.push('请设置环境变量 WECHAT_BINDING_TOKEN_SECRET，长度至少16位');
      }
      if (error.includes('企业微信CorpID')) {
        suggestions.push('请设置环境变量 WECHAT_CORP_ID 或检查 weChatConfig.WECHAT_CORP_ID');
      }
      if (error.includes('微信开放平台AppID')) {
        suggestions.push('请设置正确的微信开放平台AppID，不能包含XXX占位符');
      }
      if (error.includes('轮询间隔')) {
        suggestions.push('请设置环境变量 WECHAT_BINDING_POLL_INTERVAL，值不能小于5000');
      }
      if (error.includes('API超时时间')) {
        suggestions.push('请设置环境变量 WECHAT_BINDING_API_TIMEOUT，值不能小于10000');
      }
    });

    return suggestions;
  }

  /**
   * 获取API完整URL
   */
  public getApiUrl(endpoint: keyof BindingConfig['api']['endpoints']): string {
    return `${this.config.api.baseUrl}${this.config.api.endpoints[endpoint]}`;
  }

  /**
   * 重新加载配置
   */
  public reloadConfig(): void {
    this.config = createBindingConfig();
    this.validated = false;
  }

  /**
   * 获取配置摘要（用于调试）
   */
  public getConfigSummary(): object {
    return {
      api: {
        baseUrl: this.config.api.baseUrl,
        timeout: this.config.api.timeout,
        retryAttempts: this.config.api.retryAttempts,
      },
      wechat: {
        corpId: this.config.wechat.corpId,
        hasCustomerServiceUrl: !!this.config.wechat.customerServiceUrl,
        hasAppId: !!this.config.wechat.openPlatform.appId,
      },
      polling: {
        interval: this.config.polling.interval,
        maxAttempts: this.config.polling.maxAttempts,
      },
      app: {
        version: this.config.app.version,
        platform: this.config.app.platform,
      },
      validated: this.validated,
    };
  }
}

// 导出配置管理器实例
export const weChatBindingConfig = WeChatBindingConfigManager.getInstance();

// 导出配置类型（用于类型检查）
export type { BindingConfig, EnvironmentConfig };
